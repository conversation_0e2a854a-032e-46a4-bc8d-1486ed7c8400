import CustomerOrderProductsMargin from './CustomerOrderProductsMargin.vue'
import { createPinia } from 'pinia'

const mockCustomerOrderProducts = [
    {
        customer_order_product_id: 1,
        article_name: 'Produit Test 1',
        article_image: '/images/test1.jpg',
        sku: 'SKU001',
        quantity: 2,
        discount_amount: 10,
        margin_details: [
            {
                selling_price_tax_included: 100,
                margin_tax_included: 20,
            },
        ],
    },
    {
        customer_order_product_id: 2,
        article_name: 'Produit Test 2',
        article_image: '/images/test2.jpg',
        sku: 'SKU002',
        quantity: 1,
        discount_amount: 5,
        margin_details: [
            {
                selling_price_tax_included: 50,
                margin_tax_included: 10,
            },
        ],
    },
]

describe('CustomerOrderProductsMargin', () => {
    beforeEach(() => {
        // Mock de l'API
        cy.intercept('POST', '**/api/erp/v1/customer-order-products', {
            statusCode: 200,
            body: {
                data: {
                    customer_order_products: mockCustomerOrderProducts,
                },
            },
        }).as('getCustomerOrderProducts')
    })

    const mountComponent = (props = {}) => {
        const pinia = createPinia()
        
        return cy.mount(CustomerOrderProductsMargin, {
            props: {
                customerOrderId: 123,
                ...props,
            },
            global: {
                plugins: [pinia],
            },
        })
    }

    context('Component initialization', () => {
        it('should render with loading state initially', () => {
            mountComponent()
            
            // Le composant ne devrait pas afficher le contenu pendant le chargement
            cy.get('[data-context="products-availability"]').should('not.exist')
        })

        it('should display content after loading', () => {
            mountComponent()
            
            cy.wait('@getCustomerOrderProducts')
            
            // Vérifier que le contenu est affiché après le chargement
            cy.get('[data-context="products-availability"]').should('exist')
            cy.contains('Marges produits').should('be.visible')
        })
    })

    context('Table display', () => {
        beforeEach(() => {
            mountComponent()
            cy.wait('@getCustomerOrderProducts')
        })

        it('should display the correct table headers', () => {
            cy.get('[data-context="erp-table"]').should('exist')
            
            // Vérifier les en-têtes de colonnes
            cy.get('[data-context="column-article"]').should('contain', 'Produit')
            cy.get('[data-context="column-quantity"]').should('contain', 'Qté')
            cy.get('[data-context="column-selling-price"]').should('contain', 'Prix de vente TTC')
            cy.get('[data-context="column-discount"]').should('contain', 'Remise TTC')
            cy.get('[data-context="column-margin"]').should('contain', 'Marge TTC')
        })

        it('should display product rows', () => {
            // Vérifier que les lignes de produits sont affichées
            cy.get('[data-context="table-row"]').should('have.length', 3) // 2 produits + 1 ligne total
        })

        it('should display total row with correct styling', () => {
            // La ligne de total devrait avoir un style différent
            cy.get('[data-context="table-row"]').last().within(() => {
                cy.contains('TOTAL').should('have.class', 'font-bold')
                cy.contains('TOTAL').should('have.class', 'text-gray-900')
            })
        })
    })

    context('Data calculations', () => {
        beforeEach(() => {
            mountComponent()
            cy.wait('@getCustomerOrderProducts')
        })

        it('should calculate totals correctly', () => {
            // Vérifier les calculs dans la ligne de total
            cy.get('[data-context="table-row"]').last().within(() => {
                // Quantité totale: 2 + 1 = 3
                cy.contains('3').should('exist')
                
                // Prix de vente total: (100 * 2) + (50 * 1) = 250
                cy.contains('250 €').should('exist')
                
                // Remise totale: 10 + 5 = 15
                cy.contains('15 €').should('exist')
                
                // Marge totale: (20 * 2) + (10 * 1) = 50
                cy.contains('50 €').should('exist')
            })
        })
    })

    context('Product display', () => {
        beforeEach(() => {
            mountComponent()
            cy.wait('@getCustomerOrderProducts')
        })

        it('should display product information correctly', () => {
            // Vérifier le premier produit
            cy.get('[data-context="table-row"]').first().within(() => {
                // Devrait contenir le composant ErpArticleItem pour les produits normaux
                cy.get('[data-testid="erp-article-item"]').should('exist')
            })
        })

        it('should display quantities correctly', () => {
            // Vérifier les quantités affichées
            cy.get('[data-context="table-row"]').eq(0).should('contain', '2')
            cy.get('[data-context="table-row"]').eq(1).should('contain', '1')
        })

        it('should format currency values correctly', () => {
            // Vérifier que les valeurs monétaires sont formatées
            cy.get('[data-context="table-row"]').eq(0).within(() => {
                cy.contains('200 €').should('exist') // 100 * 2
                cy.contains('10 €').should('exist')  // discount
                cy.contains('40 €').should('exist')  // 20 * 2
            })
        })
    })

    context('Error handling', () => {
        it('should handle API errors gracefully', () => {
            // Mock d'une erreur API
            cy.intercept('POST', '**/api/erp/v1/customer-order-products', {
                statusCode: 500,
                body: { error: 'Server error' },
            }).as('getCustomerOrderProductsError')

            mountComponent()
            
            cy.wait('@getCustomerOrderProductsError')
            
            // Vérifier qu'un toast d'erreur est affiché
            cy.get('[data-context="toast-container"]').should('exist')
        })
    })

    context('Props validation', () => {
        it('should accept different customerOrderId', () => {
            mountComponent({ customerOrderId: 456 })
            
            cy.wait('@getCustomerOrderProducts').then((interception) => {
                expect(interception.request.body.where._and[0].customer_order_id._eq).to.equal(456)
            })
        })
    })

    context('Empty state', () => {
        it('should handle empty product list', () => {
            // Mock d'une réponse vide
            cy.intercept('POST', '**/api/erp/v1/customer-order-products', {
                statusCode: 200,
                body: {
                    data: {
                        customer_order_products: [],
                    },
                },
            }).as('getEmptyCustomerOrderProducts')

            mountComponent()
            
            cy.wait('@getEmptyCustomerOrderProducts')
            
            // Le tableau devrait être vide
            cy.get('[data-context="table-row"]').should('not.exist')
        })
    })
})
