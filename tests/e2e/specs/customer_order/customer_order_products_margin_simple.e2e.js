describe('Customer order products margin', () => {
    const VISIT_PAGE = (file) => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {},
            },
        }).as('customer_order')

        cy.intercept('POST', '**/api/erp/v1/customer-order-products', { fixture: file }).as(
            'cpost_customer_order_products',
        )

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait('@customer_order')
        cy.emitEvent('open-products-margin', 123456)
        cy.wait('@cpost_customer_order_products')
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Displays customer order products margins', () => {
        it('opens the margin panel', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            // Check that slide-out container exists
            cy.get('[data-context=slide-out-container]').should('exist')
            
            // Check that the products-margin panel exists
            cy.get('[data-context=products-margin]').should('exist')
        })

        it('displays page header', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=products-margin]')
                .find('[data-context=page-header]')
                .should('contain', 'Marges produits')
        })

        it('displays table with data', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=products-margin]')
                .find('[data-context=erp-table]')
                .should('exist')
            
            // Should have some table rows
            cy.get('[data-context=products-margin]')
                .find('[data-context=table-row]')
                .should('have.length.greaterThan', 0)
        })
    })
})
