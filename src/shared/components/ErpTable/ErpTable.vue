<script lang="ts" setup>
import ErpTableColumnTitle from '@/shared/components/ErpTable/ErpTableColumnTitle.vue'
import ErpCheckbox from '@/shared/components/form/ErpCheckbox.vue'
import { DEFAULT_STYLES, STYLE_PRESETS, STYLE_VERTICAL_ALIGN } from '@/shared/components/ErpTable'
import type { ErpTableColumnDefinition, ErpTableDefaultStyles, ErpTableRow } from '@/shared/components/ErpTable'
import ErpSkeleton from '@/shared/components/ui/ErpSkeleton.vue'
import { SORT_DIRECTION } from '@/shared/composable/usePager/types'
import { startCase, kebabCase } from '@/shared/lodash_loader'
import type { ValueOf } from '@/shared/type_utils'
import type { ComputedRef, Ref } from 'vue'
import { computed, ref, watch } from 'vue'
// @ts-ignore-next-line
import { faSort, faSortUp, faSortDown } from '@son-video/font-awesome-pro-duotone/index.es'

export interface ErpTableProps {
    /**
     * Configuration des colonne(s) du tableau
     */
    columns: string[] | ErpTableColumnDefinition[]
    /**
     * Page sur laquelle on se trouve
     * Utiliser de manière interne pour la numérotation des lignes
     */
    currentPage?: number
    /**
     * Si `true`, les lignes du tableau sera plus foncée lors du survol
     */
    hover?: boolean
    /**
     * Affiche le masque de chargement si `true`
     */
    isLoading?: boolean
    /**
     * Nombre d'élément par page
     * Utiliser de manière interne pour la numérotation des lignes
     */
    itemsPerPage?: number
    /**
     * Si `true`, le wrapper du tableau n'affichera pas de bordure
     * Utile si le tableau est embedded dans un panel qui à ses propres bordures
     */
    noBorder?: boolean
    /**
     * Si `true`, la props `presets` sera ignoré
     */
    noPresets?: boolean
    /**
     * Style pré-défini du tableau
     */
    presets?: Array<ValueOf<typeof STYLE_PRESETS>>
    /**
     * Données du tableau
     */
    rows: ErpTableRow[]
    /**
     * Si `true`, une colonne avec une checkbox permettant de sélectionner une ou plusieurs colonnes
     * sera affichée.
     */
    selectable?: boolean
    /**
     * Si `false`, les headers des colonnes ne seront pas affichés
     */
    showHeader?: boolean
    /**
     * Nom de la colonne actuellement triée
     * Affiche une icône (flèche) sur la colonne triée
     */
    sortBy?: string | null
    /**
     * Sens du tri de la colonne triée
     */
    sortDirection?: ValueOf<typeof SORT_DIRECTION>
    /**
     * Si `true`, le tableau aura un fond contrasté une ligne sur deux
     */
    striped?: boolean
    /**
     * Si `true`, les entêtes du tableau seront sticky (uniquement css donc la props seule ne garantie pas le fonctionnement dans toute les conditions)
     */
    sticky?: boolean
    /**
     * Override de style, fusionné avec les styles par défaut
     * (voir constante `DEFAULT_STYLES` dans le composant)
     */
    styles?: {}
    /**
     * Alignement dans le tableau
     */
    verticalAlign?: keyof typeof STYLE_VERTICAL_ALIGN
    trackBy?: string | null
}

const props = withDefaults(defineProps<ErpTableProps>(), {
    currentPage: () => 1,
    hover: () => false,
    isLoading: () => false,
    itemsPerPage: () => 15,
    noBorder: () => false,
    noPresets: () => false,
    presets: () => [STYLE_PRESETS.spreadsheet],
    selectable: () => false,
    showHeader: () => true,
    sortBy: () => null,
    sortDirection: () => SORT_DIRECTION.asc,
    striped: () => false,
    sticky: () => false,
    styles: () => {
        return {}
    },
    verticalAlign: () => 'top',
    trackBy: () => null,
})

export interface ErpTableEmits {
    /**
     * Quand une colonne triable est cliqué
     */
    (event: 'column:sort', payload: { sortBy: string; sortDirection: ValueOf<typeof SORT_DIRECTION> }): void
    /**
     * Évènement émis quand une ligne du tableau est cliqué
     */
    (event: 'tr:click', payload: ErpTableRow): void
    /**
     * Évènement émis quand on change le statut coché d'une, ou, toutes les lignes affichées (seulement lorsque la props selectable est à true)
     * Passe en paramètre la liste des lignes cochées/sélectionnées.
     */
    (event: 'rows:selected', payload: Array<ErpTableRow>): void
}

const emit = defineEmits<ErpTableEmits>()

const warnings: Ref<Array<string>> = ref([])

/**
 * Format all columns in order to have all the required fields in an object form
 */
const computed_columns: ComputedRef<Array<ErpTableColumnDefinition>> = computed(() =>
    structuredClone(props.columns).map((column: ErpTableColumnDefinition | string) => {
        return Object.assign(
            {
                sortable: false,
                title: startCase(typeof column === 'string' ? column : column.name),
            },
            typeof column === 'string' ? { name: column } : column,
        )
    }),
)

/**
 * Extract column names
 */
const column_names: ComputedRef<Array<string>> = computed(() => computed_columns.value.map((c) => c.name))

/**
 * Merge default style with the one provided via the props
 */
const computed_styles: ComputedRef<ErpTableDefaultStyles> = computed(() =>
    Object.assign({}, DEFAULT_STYLES, props.styles),
)

// ######################## //
// ## Methods            ## //
// ######################## //

/**
 * Warn user about a misconfiguration
 * Data is used in order to warn only once when the warning sent from a loop
 */
function warn(message: string, args: string[] = []) {
    if (!warnings.value.includes(message)) {
        console.warn(message, ...args)
        warnings.value.push(message)
    }
}

function getRowNumber(index: number): number {
    return props.currentPage > 1 ? props.currentPage + index : props.currentPage - props.itemsPerPage + (index + 1)
}

/**
 * Computed CSS classes to use on each row "tr"
 * Useful to highlight a row when/if needed
 */
function getRowClass(row: ErpTableRow, index: number): string[] {
    const classes = [typeof row?._rowClasses === 'string' ? row._rowClasses : '']

    if (index > 0 || !props.noBorder || props.showHeader) {
        classes.push(computed_styles.value.tbody_tr)
    }

    if (props.striped) {
        classes.push(computed_styles.value.tbody_tr_striped)
    }

    if (props.hover) {
        classes.push(computed_styles.value.tbody_tr_hover)
    }

    return classes
}

const internal_rows = ref<ErpTableRow[]>([])

watch(
    () => props.rows,
    (newValue) => {
        internal_rows.value = structuredClone(newValue).map((row) => {
            // No need to compute those without the selectable props set to true
            if (props.selectable) {
                row._isSelectable = !props.selectable ? false : row._isSelectable ?? true
                row._isChecked = !props.selectable ? false : row._isChecked ?? false
            }

            return row
        })
    },
    { immediate: true, deep: true },
)

/**
 * Computed CSS classes to use on each row "tr"
 * Useful to highlight a row when/if needed
 */
function getRowClasses(): Array<string | string[]> {
    const row_classes: Array<string | string[]> = []
    internal_rows.value.forEach((row, rowIndex: number) => (row_classes[rowIndex] = getRowClass(row, rowIndex)))

    return row_classes
}

/**
 * Compute styles shared by both th and td elements
 *
 * @param classes
 * @param index
 * @param column
 */
function computeStylesTo(
    classes: (string | string[])[],
    index: number,
    column: {} | ErpTableColumnDefinition = {},
): void {
    const is_first = 0 >= index
    const is_last = props.columns.length === index + 1
    const no_padding = !!('noPadding' in column ? column.noPadding : false)

    if (is_first && !no_padding) {
        classes.push(computed_styles.value.padding_first)
    }

    if (!is_first && !no_padding) {
        classes.push(computed_styles.value.padding_default)
    }

    if (is_last && !no_padding) {
        classes.push(computed_styles.value.padding_last)
    }

    // presets
    if (!is_last && !props.noPresets && props.presets.includes(STYLE_PRESETS.spreadsheet)) {
        classes.push(computed_styles.value.tbody_td_spreadsheet_preset)
    }

    if (
        !props.noPresets &&
        !!('autoExpand' in column ? column.noPadding : false) &&
        props.presets.includes(STYLE_PRESETS.constrain_all)
    ) {
        classes.push(computed_styles.value.tbody_td_spreadsheet_preset)
    }
}

/**
 * For each row we check if there is some data to extract
 * If there is no data, we show the column name to indicate with which slot key the row can be used
 */
function getDataFromRowWithColumnName(row: ErpTableRow, name: string) {
    return name in row ? row[name] : `[slot: ${name}]`
}

/**
 * Returns the name of the sortable column
 */
function getSortKey(column: ErpTableColumnDefinition): string {
    return typeof column.sortable === 'string' ? column.sortable : column.name
}

/**
 * Tells whether a column is sorted or not
 * This is just UI wise, nothing prevent the user to sort its column on server side only
 */
function isSorted(column: ErpTableColumnDefinition): boolean {
    // If a column is marked as sortable, sortBy props should be defined
    // If not the user should be warned as it might have forgotten to set the prop
    if (typeof props.sortBy === 'undefined') {
        warn(`"sortBy" prop was not defined, but column "%s" is marked as sortable`, [column.name])
    }

    return getSortKey(column) === props.sortBy
}

/**
 * Emit an event to the parent component with the sorted key and sorted direction
 * This component is not responsible for the actual sorting whether client side or server side
 */
function sort(column: ErpTableColumnDefinition) {
    emit('column:sort', {
        sortBy: getSortKey(column),
        sortDirection: props.sortDirection === 'asc' ? 'desc' : 'asc',
    })
}

function getRowInternalKey(row: ErpTableRow): string {
    if (props.trackBy && row?.[props.trackBy]) {
        return row[props.trackBy].toString()
    }

    return 'not-unique'
}

// ######################## //
// ## Computed : STYLES  ## //
// ######################## //

const wrapper_classes = computed(() => {
    if (!props.noBorder && !props.showHeader) {
        return [computed_styles.value.wrapper_no_header]
    }

    return [!props.noBorder ? computed_styles.value.wrapper : computed_styles.value.wrapper_no_border]
})

const tbody_classes = computed(() => {
    return !props.striped ? computed_styles.value.tbody_default : computed_styles.value.tbody_striped
})

/**
 * Compute the column css classes that will be applied on each column td elements
 */
const cell_classes = computed(() => {
    let cell_classes: {
        [key: string]: Array<string | string[]>
    } = {}

    computed_columns.value.forEach((column, index) => {
        const classes = [computed_styles.value.tbody_td, STYLE_VERTICAL_ALIGN[props.verticalAlign]]

        computeStylesTo(classes, index, column)

        const use_compact_style = (column?.compact ?? false) || props.presets.includes(STYLE_PRESETS.compact)
        const use_no_padding_style = column?.noPadding ?? false

        if (use_compact_style && !use_no_padding_style) {
            classes.push(computed_styles.value.tbody_td_compact)
        }

        if (!use_compact_style && !use_no_padding_style) {
            classes.push(computed_styles.value.tbody_td_normal)
        }

        if (use_no_padding_style) {
            classes.push('p-0')
        }

        cell_classes[column.name] = [...classes, column?._columnClasses || '', column?._cellClasses || '']
    })

    return cell_classes
})

const cell_classes_selectable = computed(() => {
    const classes = [
        computed_styles.value.tbody_td,
        STYLE_VERTICAL_ALIGN[props.verticalAlign],
        computed_styles.value.tbody_td_normal,
        computed_styles.value.tbody_td_spreadsheet_preset,
        computed_styles.value.padding_first,
    ]

    computeStylesTo(classes, -1)

    return classes
})

/**
 * Compute the column css classes that will be applied on each column th elements
 */
const th_classes = computed(() => {
    let th_classes: {
        [key: string]: Array<string | string[]>
    } = {}
    computed_columns.value.forEach((column, index) => {
        const classes: Array<string | string[]> = [
            computed_styles.value.thead_th_no_border,
            computed_styles.value.thead_th_shared,
            props.presets.includes(STYLE_PRESETS.compact)
                ? computed_styles.value.thead_th_compact
                : computed_styles.value.thead_th_default,
            column?._columnClasses || '',
        ]

        if (props.sticky) {
            classes.push(computed_styles.value.thead_th_sticky)
        }

        computeStylesTo(classes, index, column)

        th_classes[column.name] = classes
    })

    return th_classes
})

const th_classes_selectable = computed(() => {
    const classes = [
        computed_styles.value.padding_first,
        computed_styles.value.thead_th_no_border,
        computed_styles.value.thead_th_shared,
        props.presets.includes(STYLE_PRESETS.compact)
            ? computed_styles.value.thead_th_compact
            : computed_styles.value.thead_th_default,
        'pr-1.5',
    ]

    computeStylesTo(classes, -1)

    return classes
})

const all_selected_status = ref(false)

const toggleAllSelected = (newStatus: boolean) => {
    if (props.selectable) {
        all_selected_status.value = newStatus
        for (const row of internal_rows.value) {
            if (row._isSelectable) {
                row._isChecked = newStatus
            }
        }
        onSelectionChange()
    }
}

function onSelectionChange() {
    emit(
        'rows:selected',
        structuredClone(internal_rows.value.filter((row) => row._isChecked)).map((row) => {
            delete row._isSelectable
            delete row._isChecked

            return row
        }),
    )
}

const has_all_rows_checked = computed(() => {
    return internal_rows.value.filter((row) => row._isChecked).length === internal_rows.value.length
})

watch(has_all_rows_checked, (newValue) => (all_selected_status.value = newValue))
</script>

<template>
    <div :class="wrapper_classes">
        <table :class="computed_styles.table" data-context="erp-table">
            <slot name="thead" :columns="computed_columns">
                <thead v-if="showHeader">
                    <tr class="border-b border-slate-200">
                        <th v-if="props.selectable" :class="th_classes_selectable">
                            <erp-checkbox
                                data-context="toggle-all-checkbox"
                                :value="all_selected_status"
                                @input="toggleAllSelected(!all_selected_status)"
                            />
                        </th>
                        <th
                            v-for="(column, index) in computed_columns"
                            :key="`column_${index}`"
                            scope="col"
                            :class="th_classes[column.name]"
                            :data-context="`column-${kebabCase(column.name)}`"
                        >
                            <!--
                        @slot Remplacement de chaque entrée de header du tableau
                        @binding {object} column Config de la colonne dont l'entête est à remplacer
                        -->
                            <slot :name="`header__${column.name}`" :column="column">
                                <span
                                    v-if="column.sortable && internal_rows.length > 0"
                                    class="flex items-center cursor-pointer group text-slate-600 hover:text-slate-800"
                                    @click.prevent="sort(column)"
                                >
                                    <erp-table-column-title :column="column" />

                                    <font-awesome-icon
                                        v-if="isSorted(column)"
                                        class="ml-2 mb-1 h-4 text-svd-500 group-hover:text-svd-600"
                                        :icon="sortDirection === 'desc' ? faSortDown : faSortUp"
                                        fixed-width
                                    />

                                    <font-awesome-icon
                                        v-if="!isSorted(column)"
                                        class="ml-2 mb-1 h-4 text-slate-400 group-hover:text-slate-600"
                                        :icon="faSort"
                                        fixed-width
                                    />
                                </span>
                                <erp-table-column-title v-else :column="column" />
                            </slot>
                        </th>
                    </tr>
                </thead>
            </slot>

            <template v-if="internal_rows.length === 0">
                <tbody :class="tbody_classes">
                    <!--
                    @slot Remplacement du `tr` lorsque la props `rows` est vide
                    @binding {object} columns Config des colonnes
                    -->
                    <slot name="no_result" :columns="columns">
                        <!-- Shows no result text when not loading -->
                        <tr>
                            <td
                                :colspan="props.selectable ? columns.length + 1 : columns.length"
                                class="text-center py-4 text-slate-500 text-sm relative"
                                data-context="table-no-result"
                            >
                                <div v-if="isLoading" class="absolute bg-white inset-0 flex flex-col items-center z-10">
                                    <erp-skeleton is-loading fit class="p-3" />
                                </div>
                                <!--
                                @slot Remplacement du texte à afficher lorsque la props `rows` est vide
                                -->
                                <slot name="no_result_content">Aucun résultat</slot>
                            </td>
                        </tr>
                    </slot>
                </tbody>
            </template>

            <!--
            @slot Remplacement de tout le `tbody` de la table
            @binding {object} rows Données du tableau
            @binding {object} columnNames Nom des colonnes
            @binding {string} rowClasses Classes CSS qui auraient été appliquées sur le `tr`
            @binding {object} styles Style (format JS) qui auraient été appliquées sur le `tr`
            @binding {object} cellClasses Styles (format JS) qui auraient été appliquées sur les `td`
            -->
            <slot
                name="tbody"
                :rows="rows"
                :column-names="column_names"
                :row-classes="getRowClasses()"
                :styles="computed_styles"
                :cell-classes="cell_classes"
            >
                <tbody :class="tbody_classes">
                    <template v-for="(row, rowIndex) in rows">
                        <!--
                        @slot Remplacement du contenu de la table
                        @binding {object} row Données du tableau
                        @binding {object} columnNames Nom des colonnes
                        @binding {string} rowClass Classes CSS qui auraient été appliquées sur le `tr`
                        @binding {number} rowIndex Index de la ligne
                        @binding {object} styles Style (format JS) qui auraient été appliquées sur le `tr`
                        @binding {object} cellClasses Styles (format JS) qui auraient été appliquées sur les `td`
                        -->
                        <slot
                            name="rows"
                            :row="row"
                            :column-names="column_names"
                            :row-class="getRowClass(row, rowIndex)"
                            :row-index="rowIndex"
                            :styles="computed_styles"
                            :cell-classes="cell_classes"
                        >
                            <!--
                            Lorsque qu'une ligne est cliqué
                            @event tr:click
                            @property {object} row Donnée de la ligne qui à été cliqué
                            -->
                            <tr
                                :key="`${rowIndex}-${getRowInternalKey(row)}`"
                                :class="getRowClass(row, rowIndex)"
                                data-context="table-row"
                                @click="$emit('tr:click', row)"
                            >
                                <td v-if="props.selectable" :class="cell_classes_selectable">
                                    <erp-checkbox
                                        v-if="internal_rows[rowIndex]._isSelectable"
                                        v-model="internal_rows[rowIndex]._isChecked"
                                        @input="onSelectionChange"
                                    />
                                    <erp-checkbox v-else disabled />
                                </td>
                                <td
                                    v-for="(name, index) in column_names"
                                    :key="`${row[name]}_${getRowNumber(rowIndex)}_${index}`"
                                    :class="cell_classes[name]"
                                    :data-context="`cell-${kebabCase(name)}`"
                                >
                                    <div
                                        v-if="isLoading"
                                        class="absolute bg-white inset-0 flex flex-col items-center z-10 opacity-80"
                                    >
                                        <erp-skeleton is-loading fit class="p-3" />
                                    </div>

                                    <!--
                                        @slot Remplacement du contenu du `td`. <br> Un slot dynamique est créé pour chaque nom de colonne à la place de `name`
                                        @binding {object} row Données du tableau
                                        @binding {number} row_number Index de la ligne
                                        -->
                                    <slot :name="name" :row="row" :row_number="getRowNumber(rowIndex)">
                                        {{ getDataFromRowWithColumnName(row, name) }}
                                    </slot>
                                </td>
                            </tr>
                        </slot>

                        <!--
                        @slot Contenu en dessous de chaque ligne
                        @binding {object} row Données du tableau
                        @binding {object} column_names Nom des colonnes
                        -->
                        <slot name="row_expander" :row="row" :column_names="column_names"></slot>
                    </template>
                </tbody>
            </slot>

            <!--
            @slot Contenu en dessous de toutes les lignes
            @binding {object} columns Config des colonnes
            -->
            <slot name="last_row" :columns="columns"></slot>

            <!--
            @slot Footer du tableau
            @binding {object} columns Config des colonnes
            -->
            <slot name="footer" :columns="columns"></slot>
        </table>
    </div>
</template>
