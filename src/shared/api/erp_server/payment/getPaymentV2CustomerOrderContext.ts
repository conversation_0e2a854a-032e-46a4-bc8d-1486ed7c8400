import { callApi } from '@/services/functions/api'
import type { PaymentV2CustomerOrderContext } from '@/shared/api/erp_server/payment/types'

import type { ApiData } from '@/shared/api/type'

export default function getPaymentV2CustomerOrderContext(customer_order_id: number) {
    return callApi<ApiData<PaymentV2CustomerOrderContext>>('get', {
        url: `${import.meta.env.VITE_APP_PAYMENT_API}/v1/customer-order/${customer_order_id}/context`,
    })
}
