# ERP Client

Application « front end » en javascript de l’ERP Son-Video.com, développée comme une SPA.

---

## D<PERSON><PERSON><PERSON> avec `pchstack`

Lister les commandes possibles pour ce projet :

```shell
phcstack erp-client
```

---

## Lancement du projet

Comme sur tous les projets impliquant du JS compilé, nous utilisons `npm` via le gestionnaire de version `nvm`.

Toutes les commandes `npm` qui suivent impliquent dans la console courante d’être dans la bonne version
de npm pour le projet.
Pour cela, il suffit de lancer une fois, à la racine du projet, la commande suivante dans le console en cours:

```bash
cd /path/to/your/project
nvm use

# si la version de node n'est pas installée, nvm proposera la commande pour installer la version nécessaire.
# Il faudra refaire un `nvm use` apres installation de cette version.
```

Installation des dépendances javascript :

```bash
cd /path/to/your/project
npm install
```

Pour accéder à l'ERP en local, il faut lancer le serveur local :

```bash
npm run serve
```

### Compiler pour la prod

```bash
npm run build
```

## Les tests

### Test unitaires

Les tests unitaires sont stocké dans le chemin suivant : `tests/unit`

Docs:  
https://facebook.github.io/jest/

Commande :

```bash
npm run test
```

### Tests E2E

```bash
# Jouer tous les tests
npm run e2e

# Jouer tous les tests en node CI (génère des fichiers de rapport de tests)
npm run e2e:ci

# Jouer les tests via l'interface web de Cypress
npm run e2e:open

# Example pour jouer une seul test via la ligne de commande
npx vue-cli-service test:e2e --headless --mode e2e --spec tests/e2e/specs/wms/carrier/carriers_list.js
```

### Organisation des fichiers de fixtures

Afin de faciliter la réutilisation et la mise à jour des fichiers de fixtures servant à mocker les appels api,
il est important que ceux-ci soient rangés dans un chemin qui reflète l’arborescence de l’api.

Exemple : des réponses possible de l’api `/api/legacy/v1/wms/obtientArticle` sont rangés
dans le répertoire `legacy/wms/obtientArticle`.

À noter que les sous répertoires `api` et `v1` ne sont pas nécessaires car trop redondant.

Si un répertoire `v2` est nécessaire, il peut être ajouté comme dernier répertoire.

### Jouer les tests via le docker de CI

-   Se connecter dans le conteneur de test : `phcstack erp-client docker-registry-exec`
-   Effacer le dossier `node_modules` : `rm -rf node_modules`
-   Installer le projet via le conteneur : `npm install`
-   Jouer les tests :
    -   `npm run unit`
    -   `npm run e2e`
    -   `npx vue-cli-service test:e2e --headless --mode e2e --spec tests/e2e/specs/wms/carrier/carriers_list.js` (Voir section tests E2E)

> La commande `npm run e2e:open` ne fonctionnera pas via le conteneur docker

Â la fin de vos tests, il vous faudra réinstaller les dépendances du projet une fois que vous aurez quitté le conteneur :

```bash
phcstack erp-client reinstall
```

### Tests Cypress sur WSL (Windows)

Suivre ce [modop](https://nickymeuleman.netlify.app/blog/gui-on-wsl2-cypress)

## Prettier

Le projet utilise [Prettier](https://prettier.io) pour formatter le code via des règles arbitraires communes à tous les devs.

Ces règles sont définies dans la clé "prettier" du fichier `package.json` du projet.
Les règles devraient être appliquées sur tous les fichiers situés dans les répertoires `src` et `tests`.

Pour utiliser Prettier, il est fortement recommandé de l'automatiser en utilisant un "File Watcher" dans PHPStorm.

### Configuration du Plugin

![image](https://gitlab.com/son-video/erp-client/uploads/6d21056ad5b24958fbf3ea6ce47ffec1/image.png)

> Si la configuration est bonne, la modification d'un fichier entrainera son formatage automatique lors de sa sauvegarde.

## Livraison

Le package sera généré à partir des sources en **local**

### Validation

```bash
cd ~/projets/erp-client/deployment
nvm use
make deploy-validation
make clean
```

### Staging

```bash
cd ~/projets/erp-client/deployment
nvm use
make deploy
make clean
```

### Production

Pour déployer en production il faut préalablement déployer sur staging puis lancer :
```bash 
phcstack erp-client deploy-prod
```

##### Si la page https://erp.son-video.work/api-docs ne fonctionne plus.

- Vérifier que le fichier https://asset.son-video.com/erp/redoc.standalone.js existe toujours dans le bucket `svd-assets-prd`
- Si le fichier ou le dossier n'existe pas:
  - Recréer le dossier `erp` si nécessaire
  - Uploader le fichier `redoc.standalone.js` présent dans le dossier docs
  - Recharger la page

> PI: le fichier est hébergé sur le CDN de la prod du site son-video.com, et par conséquent a une durée de rétention
> de 180 jours. Au delà de ce délai le fichier est automatiquement supprimé et le dossier le contenant aussi s'il deviens vide.
