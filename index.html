<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
      <title>Son-Vidéo ERP</title>

      <!-- Hack to allow redoc to work properly : https://github.com/Redocly/redoc/issues/2055 -->
      <script type="module">import process from "process"; window.process = process; </script>

      <link rel="icon" type="image/x-icon" href="/favicon.ico"/>
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png"/>
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png"/>
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png"/>
      <link rel="manifest" href="/manifest.json"/>
      <link rel="mask-icon" href="/safari-pinned-tab.svg"/>
      <link rel="shortcut icon" href="/favicon.ico"/>
      <meta name="theme-color" content="#ffffff">
  </head>
  <body>
      <noscript>
          <strong>We're sorry but the ERP doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
      </noscript>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
