describe('Customer order products margin', () => {
    const VISIT_PAGE = (file) => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {},
            },
        }).as('customer_order')

        cy.intercept('POST', '**/api/erp/v1/customer-order-products', { fixture: file }).as(
            'cpost_customer_order_products',
        )

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait('@customer_order')
        cy.emitEvent('open-products-margin', 123456)
        cy.wait('@cpost_customer_order_products')
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Displays customer order products margins', () => {
        it('opens the margin panel', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            // Check that slide-out container exists
            cy.get('[data-context=slide-out-container]').should('exist')

            // Check that the products-margin panel exists
            cy.get('[data-context=products-margin]').should('exist')

            cy.get('[data-context=products-margin]')
                .find('[data-context=page-header]')
                .should('contain', 'Marges produits')
        })

        it('displays correct table headers', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=products-margin]').as('panel')

            // Check table headers
            cy.get('@panel').find('[data-context=erp-table] thead tr th').as('headers')
            cy.get('@headers').should('have.length', 6)
            cy.get('@headers').eq(0).should('contain', 'Produit')
            cy.get('@headers').eq(1).should('contain', 'Qté')
            cy.get('@headers').eq(2).should('contain', 'Prix de vente TTC')
            cy.get('@headers').eq(3).should('contain', 'Remise TTC')
            cy.get('@headers').eq(4).should('contain', 'Marge TTC')

            cy.get('@panel').find('[data-context=table-row]').should('have.length', 4)

            // Check first product data
            cy.get('@panel')
                .find('[data-context=table-row]')
                .checkRows([
                    [['JAMOS809HCSBNR', 'S809 HCS Bois noir'], '2', '599,98 €', '15,50 €', '190,50 €'],
                    [['JAMOS810SUBNR', 'S810 SUB Bois noir'], '1', '199,99 €', '8,00 €', '65,50 €'],
                    [['CABHDMI2M', 'Câble HDMI 2m'], '3', '38,97 €', '2,50 €', '23,55 €'],
                    ['TOTAL', '6', '838,94 €', '26,00 €', '279,55 €'],
                ])
        })

        it('handles empty product list', () => {
            cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
                statusCode: 200,
                body: {
                    data: {},
                },
            }).as('customer_order_empty')

            cy.intercept('POST', '**/api/erp/v1/customer-order-products', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        customer_order_products: [],
                    },
                },
            }).as('cpost_customer_order_products_empty')

            cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')
            cy.wait('@customer_order_empty')
            cy.emitEvent('open-products-margin', 123456)
            cy.wait('@cpost_customer_order_products_empty')

            cy.get('[data-context=products-margin]').as('panel').should('exist')
            cy.get('@panel').find('[data-context=page-header]').should('contain', 'Marges produits')
            cy.get('@panel').find('[data-context=table-row]').should('not.exist')
        })
    })
})
