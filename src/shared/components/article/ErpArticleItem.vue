<script lang="ts" setup>
import type { ArticleStatuses } from '@/shared/api/erp_server/article/types'
import CopyButton from '@/shared/components/buttons/CopyButton.vue'
import StatusDelayBadge from '@/shared/components/ui/StatusDelayBadge.vue'
import Badge from '@/shared/components/ui/Badge.vue'
import StatusIndicator from '@/shared/components/ui/StatusIndicator.vue'
import ErpLink from '@/shared/components/link/ErpLink.vue'
import ErpTooltip from '@/shared/components/ui/ErpTooltip.vue'
import type { ValueOf } from '@/shared/type_utils'
import { Menu as VMenu } from 'floating-vue'
import { AVAILABILITY } from '@/shared/referential/product/availability'
import { HOME_GROUP_BRANDS } from '@/shared/referential/product/group_brand'
import { formatCurrency, prependCdnToPath } from '@/shared/string_utils'
import { computed } from 'vue'

// Redeclaration of the same props as in interface in useErpArticleItem (ArticleItemProps)
// Vue 2.x only support inline interface for props
// imports is supported since Vue 3.3 but won't be supported EVER in Vue 2.7
// @see : https://github.com/vuejs/core/issues/4294#issuecomment-1724937142
interface Props {
    sku: string
    name?: string
    image?: string
    price?: string | number
    delay?: number | null
    status?: ValueOf<typeof ArticleStatuses> | null
    compact?: boolean
    targetBlank?: boolean
    noLink?: boolean
    showActions?: boolean
    groupBrand?: string
    isUnbasketable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    name: undefined,
    image: undefined,
    price: undefined,
    delay: undefined,
    status: null,
    compact: () => false,
    targetBlank: () => false,
    noLink: () => false,
    showActions: () => false,
    groupBrand: () => '',
    isUnbasketable: () => false,
})

const to = computed(() => ({
    name: 'article_base',
    params: { article_id_or_sku: props.sku },
}))

const status_indicator_color = computed(() => AVAILABILITY.find((a) => a.value === props.delay)?.indicator ?? 'red')
</script>

<template>
    <!-- COMPACT MODE -->
    <v-menu v-if="compact" placement="left">
        <div class="flex gap-3 p-3 text-sm items-center" data-context="erp-article-item">
            <status-indicator v-if="props.status" :color="status_indicator_color" />

            <span v-if="props.noLink" class="text-svd-500 font-medium uppercase truncate" data-context="sku">{{
                props.sku
            }}</span>
            <erp-link v-else :to="to" class="truncate" data-context="sku" :target="props.targetBlank ? '_blank' : ''">
                {{ props.sku }}
            </erp-link>

            <div class="ml-auto">
                <span v-if="props.price" class="text-slate-500 whitespace-nowrap" data-context="price">{{
                    formatCurrency(props.price)
                }}</span>

                <!-- @slot Available in both mode. Is always on the left in last "column" -->
                <slot name="after" />
            </div>
        </div>

        <template v-if="props.status" #popper>
            <div class="p-2">
                <status-delay-badge :delay="props.delay ?? undefined" :status="props.status" />
            </div>
        </template>
    </v-menu>

    <!-- COMPLETE MODE -->
    <div v-else class="flex gap-3 p-3 text-sm items-start" data-context="erp-article-item">
        <div class="flex flex-col flex-shrink-0">
            <div class="flex justify-center items-center h-10 w-10 border border-slate-100">
                <img
                    class="max-h-full"
                    :src="props.image || prependCdnToPath('/images/ui/uiV3/graphics/no-img-300.png')"
                    data-context="image"
                    :alt="props.sku"
                />
            </div>
        </div>
        <div v-if="props.showActions" class="flex flex-col gap-2 -mx-1 pt-1" data-context="actions">
            <copy-button :copy_value="props.sku" data-context="copy-sku" />

            <!-- @slot Use this slot to add actions below the "copy-sku" button, only available in details mode -->
            <slot name="more-actions" />
        </div>
        <div class="flex flex-col gap-1 truncate" :class="props.name ? '' : 'mt-3'">
            <span v-if="props.noLink" class="text-gray-900 font-medium text-sm uppercase truncate" data-context="sku">{{
                props.sku
            }}</span>
            <erp-link v-else :to="to" class="truncate" data-context="sku" :target="props.targetBlank ? '_blank' : ''">
                {{ props.sku }}
            </erp-link>

            <span class="text-gray-900 font-light whitespace-normal -mt-1" data-context="name">{{ props.name }}</span>

            <div class="flex flex-wrap gap-1">
                <status-delay-badge
                    v-if="props.status"
                    :delay="props.delay ?? undefined"
                    :status="props.status"
                    :is-unbasketable="props.isUnbasketable"
                />

                <erp-tooltip
                    v-if="props.groupBrand !== '' && HOME_GROUP_BRANDS.get(props.groupBrand)"
                    :message="props.groupBrand"
                >
                    <badge>
                        <span data-context="group_brand">{{ HOME_GROUP_BRANDS.get(props.groupBrand) }}</span>
                    </badge>
                </erp-tooltip>
            </div>

            <!--
            @slot Contenu par défaut, affiché sous le sku, nom du produit et le badge de dispo (si présent pour ce dernier)
            -->
            <slot />
        </div>

        <div class="ml-auto">
            <span v-if="props.price" class="text-slate-500 whitespace-nowrap" data-context="price">{{
                formatCurrency(props.price)
            }}</span>

            <slot name="after" />
        </div>
    </div>
</template>
