<script setup lang="ts">
import CustomerOrderPaymentsBlock from '@/apps/erp/components/CustomerOrder/CustomerOrderPaymentsBlock.vue'
import {
    useCustomerOrderPaymentV2Store,
    useCustomerOrderStore,
} from '@/apps/erp/components/CustomerOrder/stores/customer_order'
import CustomerOrderAntiFraudModule from '@/apps/erp/components/CustomerOrder/AntiFraudModule/CustomerOrderAntiFraudModule.vue'
import BeginStorePickupForm from '@/apps/erp/components/CustomerOrder/BeginStorePickupForm.vue'
import PaymentV2OperationSlideIn from '@/apps/erp/components/CustomerOrder/Payment/PaymentV2OperationSlideIn.vue'
import Wrapper from '@/apps/legacy/components/Wrapper.vue'
import ErpTooltip from '@/shared/components/ui/ErpTooltip.vue'
import ScopedBadge from '@/shared/components/ui/ScopedBadge.vue'
import CustomerOrderPaymentActions from '@/apps/erp/components/CustomerOrderPayment/CustomerOrderPaymentActions.vue'
import { getDeliveryNote } from '@/shared/api/erp_server'
import { cPostCustomerOrders } from '@/shared/api/erp_server/customer_order/cPostCustomerOrders'
import { ONGOING } from '@/shared/referential/customer_order/status_types'
import { CHAMPIGNY2_ID } from '@/shared/referential/wms/warehouses'
import ErpButton from '@/shared/components/buttons/ErpButton.vue'
import CustomerOrderProductsAvailability from '@/apps/erp/components/CustomerOrder/CustomerOrderProductsAvailability.vue'
import CustomerOrderProductsMargin from '@/apps/erp/components/CustomerOrder/CustomerOrderProductsMargin.vue'
import CustomerOrderTimeline from '@/apps/erp/components/CustomerOrder/Events/CustomerOrderTimeline.vue'
import ErpSkeleton from '@/shared/components/ui/ErpSkeleton.vue'
import Alert from '@/shared/components/ui/Alert.vue'
import Badge from '@/shared/components/ui/Badge.vue'
import { EASYLOUNGE } from '@/shared/referential/customer_order/origins'
import InvoiceEzl from '@/apps/erp/components/Marketplace/EasyLounge/InvoiceEzl.vue'
import PageHeader from '@/shared/components/ui/PageHeader.vue'
import { storeToRefs } from 'pinia'
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import useMessageReceivedFromLegacyIframe, {
    type Reactions,
} from '@/shared/composable/useMessageReceivedFromLegacyIframe'
import { useRoute, useRouter } from 'vue-router/composables'
import SlideOutContainer from '@/shared/components/ui/SlideOutContainer.vue'
// @ts-ignore-next-line
import { faCommentAltLines, faHandHoldingBox, faFileInvoice } from '@son-video/font-awesome-pro-solid/index.es'
import { postCloneCustomerOrder } from '@/shared/api/erp_server/customer_order/postCloneCustomerOrder'
import { useToastStore } from '@/services/plugin/toast/stores'
import CustomerOrderStatus from '@/apps/erp/views/CustomerOrder/CustomerOrderStatus.vue'
import DeliveryDateBadge from '@/shared/components/customer_order/DeliveryDateBadge.vue'

const route = useRoute()
const router = useRouter()
const is_shown = ref(true)

onMounted(async () => {
    // EasyLounge load via their own external id, it works in the legacy iframe but not the refactored VueJs page
    // We need to redirect the page internally to the correct endpoint
    if (route.query?.no_commande_origine !== undefined) {
        const redirect_to = ref<undefined | number>(undefined)
        try {
            const result = await cPostCustomerOrders({
                where: {
                    original_customer_order_id: {
                        _eq: route.query.no_commande_origine,
                    },
                },
            })

            redirect_to.value = result.data.customer_orders?.[0].customer_order_id
        } catch (Error) {
            // do nothing
        }

        if (!redirect_to.value) {
            router.push({
                name: 'error_page',
                params: {
                    code: '404',
                    message: `La commande avec numéro d'origine "${route.query.no_commande_origine}" n'a pas été trouvée`,
                },
            })

            return
        }

        router.push({
            name: 'legacy_customer_order',
            query: {
                id_commande: redirect_to.value,
            },
        })
    }
})

const store = useCustomerOrderStore()
const { is_loading, customer_order, is_amazon_business, is_duty_free, source } = storeToRefs(store)

const prevent_reload = ref(false)
const collect_opened = ref(false)
const is_open_payment_action_form = ref(false)
const is_opened_availability = ref(false)
const is_opened_products_margin = ref(false)
const is_timeline_opened = ref(false)
const is_invoice_ezl_opened = ref(false)
const transaction_id = ref<number | null>(null)
const customer_order_id = ref<number | null>(null)
const delivery_notes = ref<{ [key: string]: any }[]>([])

const reactions: Reactions = {
    'content-fully-loaded': () => {
        if (!is_loading.value && prevent_reload.value === false) {
            load()
        }

        if (!is_loading.value && prevent_reload.value) {
            prevent_reload.value = false
        }
    },
    'open-remit-dialog': (payload) => {
        transaction_id.value = payload.customer_order_payment_id
        is_open_payment_action_form.value = true
    },
    'open-products-availability': (payload) => {
        customer_order_id.value = payload
        is_opened_availability.value = true
    },
    'open-products-margin': (payload) => {
        customer_order_id.value = payload
        is_opened_products_margin.value = true
    },
    'handle-payment': (payload) => {
        useCustomerOrderPaymentV2Store().viewDetailsOnOperation(payload.operationId)
    },
    'clone-customer-order': (payload) => {
        const { customer_order_id } = payload
        try {
            is_loading.value = true
            postCloneCustomerOrder(customer_order_id)
                .then((response) => {
                    router.push({
                        name: 'legacy_customer_order',
                        query: {
                            id_commande: response.data.customer_order_id,
                        },
                    })
                })
                .catch(() => useToastStore().add({ content: 'Une erreur est survenue' }))
        } catch (e) {
            useToastStore().add({ content: 'Une erreur est survenue' })
        } finally {
            is_loading.value = false
        }
    },
}

const { reactToMessage } = useMessageReceivedFromLegacyIframe(reactions)

const cannot_generate_delivery_ticket = computed(() => is_undefined_carrier.value || is_inconsistent_carrier.value)
const is_undefined_carrier = computed(
    () => customer_order.value && (customer_order.value.carrier_id === 1 || customer_order.value.carrier_id === null),
)

const is_inconsistent_carrier = computed(() => customer_order.value && customer_order.value.has_inconsistent_carrier)
// Based on the order alone, indicates if its delivery notes may be collected
const is_order_maybe_collectible = computed(
    () =>
        customer_order.value &&
        !customer_order.value.has_inconsistent_carrier &&
        customer_order.value.pickup_store_id === CHAMPIGNY2_ID &&
        customer_order.value.status === ONGOING &&
        customer_order.value.is_paid &&
        customer_order.value.delivery_note_ids.length > 0,
)
// Ids of the delivery notes of interest
const delivery_notes_ids_to_retrieve = computed(() => {
    if (!customer_order.value) {
        return []
    }

    return is_order_maybe_collectible.value ? customer_order.value.delivery_note_ids : []
})
// Delivery notes that can be collected
const collectable_delivery_notes = computed(() =>
    delivery_notes.value.filter((dn) => is_order_maybe_collectible.value && dn.store_pickup_started_at === null),
)

const is_being_imported = computed(() => customer_order.value?.tags?.some((t) => t.name === 'status.import') ?? false)
const customer_order_id_from_url_query_string = computed(() => route.query.id_commande)
const is_loading_iframe = computed(
    () =>
        (null === customer_order.value && 'undefined' !== typeof customer_order_id_from_url_query_string.value) ||
        false === is_shown.value,
)

watch(
    delivery_notes_ids_to_retrieve,
    (ids) => {
        retrieveDeliveryNotes(ids)
    },
    { immediate: true },
)

watch(
    customer_order_id_from_url_query_string,
    (new_value, old_value) => {
        if (new_value !== old_value && new_value) {
            load()
        }
    },
    { immediate: true },
)

function load() {
    if (customer_order_id_from_url_query_string.value) {
        prevent_reload.value = true
        useCustomerOrderStore().load(customer_order_id_from_url_query_string.value)
        useCustomerOrderPaymentV2Store().loadByCustomerOrderId(customer_order_id_from_url_query_string.value)
    }
}

function onPageRefresh() {
    is_open_payment_action_form.value = false

    // Reload the iframe by forcing the component to be re-rendered
    is_shown.value = false
    load()
    nextTick(() => {
        is_shown.value = true
    })
}

/**
 * Load delivery notes from their ids
 *
 * @param ids
 */
function retrieveDeliveryNotes(ids) {
    if (ids.length === 0) {
        delivery_notes.value = []
    } else {
        Promise.all(ids.map(getDeliveryNote))
            .then((responses) => {
                delivery_notes.value = responses.map((response) => response.data.delivery_note)
            })
            .catch(() => (delivery_notes.value = []))
    }
}

/**
 * Close slide-out and refresh delivery notes data
 */
function closeAndRefreshDeliveryNotes() {
    collect_opened.value = false
    retrieveDeliveryNotes(delivery_notes_ids_to_retrieve.value)
}

const is_shipped = computed(() => customer_order.value?.effective_shipping_date !== null)
</script>
<template>
    <div>
        <page-header>
            <div class="flex items-center gap-2">
                <span>Commande client</span>

                <template v-if="customer_order">
                    <erp-tooltip
                        v-if="customer_order_id_from_url_query_string != customer_order.original_customer_order_id"
                        message="N° de commande d'origine"
                    >
                        <span class="text-amber-600">{{ customer_order.original_customer_order_id }}</span>
                    </erp-tooltip>
                    <erp-tooltip message="N° de commande Son-Vidéo.com">
                        <span class="text-svd-500">{{ customer_order_id_from_url_query_string }}</span>
                    </erp-tooltip>
                    <customer-order-status :value="customer_order.computed_status" />
                </template>
            </div>

            <template v-if="customer_order" #more>
                <div class="flex items-center px-3 gap-3">
                    <!-- source-->
                    <scoped-badge
                        v-if="source?.taxonomy_meta?.source_group"
                        scope="source"
                        color="yellow"
                        data-context="source"
                    >
                        {{ source?.taxonomy_meta?.source_group }} - {{ source?.taxonomy_meta?.source }}
                    </scoped-badge>

                    <scoped-badge color="yellow" scope="origine" data-context="origin">
                        {{ customer_order.sales_channel_origin }}
                        <span v-if="customer_order.store_label" class="text-xs">
                            - {{ customer_order.store_label }}</span
                        >
                    </scoped-badge>

                    <scoped-badge v-if="is_amazon_business" scope="business" data-context="business" color="yellow">
                        <span v-if="is_duty_free" data-context="duty-free">HT</span>
                        <span v-else data-context="all-tax-included">TTC</span>
                    </scoped-badge>

                    <badge
                        v-if="customer_order.has_ongoing_premium_warranty"
                        color="yellow"
                        data-context="premium-warranty-tag"
                        >Garantie premium
                    </badge>

                    <delivery-date-badge
                        v-if="!is_shipped && customer_order.initial_estimated_delivery_date"
                        class="hidden"
                        :initial-date="customer_order.initial_estimated_delivery_date"
                        :current-date="customer_order.current_estimated_shipping_date"
                        :updated-at="customer_order.shipping_date_updated_at"
                    />

                    <!-- Error message -->
                    <div v-if="cannot_generate_delivery_ticket" class="flex items-center pl-3">
                        <div
                            v-if="is_undefined_carrier"
                            class="p-2 pl-3 pr-3 font-medium bg-red-100 text-red-900 border border-red-700"
                            data-context="undefined_carrier_message"
                        >
                            <span v-html="$t('erp_customer_order:undefined_carrier_message')"></span>
                        </div>
                        <div
                            v-if="is_inconsistent_carrier"
                            class="p-2 pl-3 pr-3 font-medium bg-red-100 text-red-900 border border-red-700"
                            data-context="inconsistent_carrier_message"
                        >
                            <span>La commande ne peut pas être préparée car le transporteur est incohérent.</span>
                        </div>
                    </div>
                </div>

                <div class="flex items-center pl-3 gap-2">
                    <erp-button
                        tertiary
                        data-context="open-timeline"
                        :icon="faCommentAltLines"
                        :icon-class="['text-blue-500']"
                        @click="is_timeline_opened = !is_timeline_opened"
                    >
                        Voir tous les messages
                    </erp-button>

                    <erp-button
                        v-if="customer_order && EASYLOUNGE === customer_order.sales_channel_origin"
                        tertiary
                        data-context="open-ezl-invoice"
                        :icon="faFileInvoice"
                        :icon-class="['text-blue-500']"
                        @click="is_invoice_ezl_opened = true"
                        >Factures EZL
                    </erp-button>

                    <!-- Collect products bouton -->
                    <erp-button
                        v-if="collectable_delivery_notes.length > 0"
                        tertiary
                        data-context="collect-products-btn"
                        :icon="faHandHoldingBox"
                        :icon-class="['text-red-600']"
                        @click="collect_opened = true"
                    >
                        Retirer les produits
                    </erp-button>
                </div>
            </template>
        </page-header>

        <!-- While the customer_order is not loaded from erp-server yet -->
        <erp-skeleton v-if="is_loading_iframe || is_loading" class="p-3" :is-loading="true" page />

        <template v-else-if="is_shown">
            <div class="flex grow">
                <div class="flex flex-col w-full" data-contex="customer-order-main">
                    <alert
                        v-if="is_being_imported"
                        info
                        flat
                        class="m-3 border bg-slate-50"
                        title="Paiement en cours"
                        data-context="import-message"
                    >
                        <p>
                            Attention, le paiement est en cours par le client. Il faut bien vérifier auprès de ce
                            dernier s'il a besoin d'assistance.<br />
                        </p>
                        <p>
                            Nous ne pouvons obtenir d'informations car le client a été redirigé vers une plateforme de
                            paiement externe à son-vidéo.com.<br />
                            Les lignes de paiement peuvent donc évoluer si ce dernier est en train de procéder au
                            paiement.
                        </p>
                        <p class="mb-0">
                            Il faut tenir compte de l'heure de création de la commande pour évaluer le niveau
                            d'assistance nécessaire.<br />
                            Au bout de 2 heures, ce message d'information disparaîtra, considérant que la commande reste
                            dans un état de paiement incohérent. Il faut alors contacter le client (appel, email,
                            devis).
                        </p>
                    </alert>

                    <customer-order-anti-fraud-module />

                    <customer-order-payments-block class="m-3" @change="onPageRefresh" />

                    <!-- legacy iframe -->
                    <wrapper wrapper-class="" @receiveMessage="reactToMessage" />
                </div>
            </div>
        </template>

        <slide-out-container :is-open="collect_opened" @close="collect_opened = false">
            <begin-store-pickup-form
                :delivery-notes="collectable_delivery_notes"
                @validate="closeAndRefreshDeliveryNotes"
            />
        </slide-out-container>

        <slide-out-container :is-open="is_open_payment_action_form" @close="is_open_payment_action_form = false">
            <customer-order-payment-actions
                v-if="transaction_id"
                :transaction-id="transaction_id"
                @refresh="onPageRefresh"
            />
        </slide-out-container>

        <slide-out-container :is-open="is_opened_availability" :size="750" @close="is_opened_availability = false">
            <customer-order-products-availability v-if="customer_order_id" :customer-order-id="customer_order_id" />
        </slide-out-container>

        <slide-out-container
            :is-open="is_opened_products_margin"
            :size="750"
            @close="is_opened_products_margin = false"
        >
            <customer-order-products-margin v-if="customer_order_id" :customer-order-id="customer_order_id" />
        </slide-out-container>

        <slide-out-container
            :is-open="is_timeline_opened"
            :size="600"
            @close="is_timeline_opened = !is_timeline_opened"
        >
            <customer-order-timeline v-if="customer_order" :customer-order-id="customer_order.customer_order_id" />
        </slide-out-container>

        <slide-out-container
            v-if="customer_order?.original_customer_order_id"
            :is-open="is_invoice_ezl_opened"
            @close="is_invoice_ezl_opened = false"
        >
            <invoice-ezl
                :customer-order-id="customer_order.customer_order_id"
                :original-customer-order-id="customer_order.original_customer_order_id"
            />
        </slide-out-container>

        <payment-v2-operation-slide-in @change="onPageRefresh" />
    </div>
</template>
