import type { ScalarUUID } from '@/shared/graphql/types'

export enum CustomerOrderPaymentType {
    PAYMENT = 'PAYMENT',
    REFUND = 'REFUND',
}

export enum CustomerOrderPaymentStatus {
    ACCEPTED = 'ACCEPTED',
    CANCELLED = 'CANCELLED',
    CREATED = 'CREATED',
    REMITTED = 'REMITTED',
}

export enum CustomerOrderPaymentWorkflow {
    V1 = 'payment_v1',
    V2 = 'payment_v2',
    LEGACY = 'legacy',
}

export enum CustomerOrderPaymentAutoStatus {
    ACCEPTED = 'accepte',
    DENIED = 'refuse',
    REMITTED = 'remise',
    CANCELLED = 'annule',
    AWAITING = 'attente',
    UNKNOWN = 'absent',
}

export interface CustomerOrderPayment {
    payment_id: number
    operation_id: null | ScalarUUID
    payment_method_id: number
    payment_method_description: null | string
    payment_method_code: null | string
    type: CustomerOrderPaymentType
    status: CustomerOrderPaymentStatus
    workflow: CustomerOrderPaymentWorkflow
    created_at: Date
    created_by: string
    created_by_name: null | string
    creation_proof: null | string
    creation_amount: number
    creation_extra_data: {
        amount?: null | number
        number?: null | string
    }
    accepted_at: null | Date
    accepted_by: null | string
    accepted_by_name: null | string
    acceptation_proof: null | string
    acceptation_amount: number
    remitted_at: null | Date
    remitted_by: null | string
    remitted_by_name: null | string
    remit_amount: number
    remit_proof: null | string
    remit_asked_at: null | Date
    remit_asked_by: null | string
    cancelled_at: null | Date
    cancelled_by: null | string
    cancelled_by_name: null | string
    cancellation_amount: number
    auto_warranty: null | string
    auto_status_detail: null | string
    auto_status: null | CustomerOrderPaymentAutoStatus
    auto_warranty_detail: null | string
    card_origin: null | string
    unpaid_at: null | Date
}

enum OrderOrigin {
    AMAZON_DE = 'amazon.de',
    AMAZON_ES = 'amazon.es',
    AMAZON_FR = 'amazon.fr',
    AMAZON_IT = 'amazon.it',
    BACKOFFICE = 'backoffice.sonvideopro.com',
    BOULANGER = 'boulanger.com',
    CDISCOUNT = 'cdiscount.com',
    CILO = 'cilo.dk',
    CULTURA = 'cultura.com',
    DARTY = 'darty.com',
    EASY_LOUNGE = 'ecranlounge.com',
    EBAY = 'ebay.fr',
    FNAC = 'fnac.fr',
    LA_REDOUTE = 'redoute',
    NUMERICABLE = 'numericable',
    MOINS_CHER_2X = '2xmoinscher.com',
    PARNASSE = 'parnasse',
    PIXMANIA = 'pixmania.com',
    PRICEMINISTER = 'priceminister.com',
    RUE_DU_COMMERCE = 'rueducommerce.fr',
    SITE = 'son-video.com',
    SITE_MOBILE = 'site_mobile',
}

export enum OverdueStatus {
    NEW = 'NEW',
    ONGOING = 'ONGOING',
    FINISHED = 'FINISHED',
    OUTDATED = 'OUTDATED',
}

export interface CustomerOrder {
    customer_order_id: number
    customer_id: number
    created_at: string | Date
    statuses: null | string[]
    computed_status: string
    amount_all_tax_included: string
    carrier_name: null | string
    store_label: null | string
    origin: OrderOrigin
    original_customer_order_id: string
    payment_fraud_detection: null | string
    source: null | string
    source_group: null | string
    /* erp-server: CustomerOrderPaymentEntity[]|null */
    payments: null | Array<any>
    articles: null | Array<CustomerOrderProduct>
    /* erp-server: DeliveryNoteEntity[]|null */
    delivery_notes: null | Array<any>
    /* erp-server: InvoiceEntity[]|null */
    invoices: Array<any>
    /* erp-server: LastInternalCommentEntity[]|null */
    last_internal_comment: Array<any>
    /* erp-server: CommentEntity[]|null */
    last_customer_comment: Array<any>
    computed_customer_firstname: null | string
    computed_customer_lastname: null | string
    overdue_status: null | keyof typeof OverdueStatus
    initial_estimated_delivery_date: null | string | Date
    current_estimated_shipping_date: null | string | Date
    shipping_date_updated_at: null | string
}

export interface CustomerOrderProduct {
    customer_order_id: number
    article_id: number
    sku: string
    quantity: number
    short_description: string
    available_quantity: number
}

export interface CustomerOrderForEditionPage {
    customer_order_id: number
    original_customer_order_id: null | string
    origin: null | string
    sales_channel_origin: null | string
    sales_channel_id: null | number
    created_at: string
    modified_at: null | string
    initial_estimated_delivery_date: null | string
    current_estimated_shipping_date: null | string
    shipping_date_updated_at: null | string
    effective_shipping_date: null | string
    status: null | string
    computed_status: string
    customer_id: number
    number_of_visits: null | number
    relay_id: null | string
    quote_id: null | number
    ip_address: null | string
    invoicing_mode: string
    should_recall_customer: boolean
    is_store_pickup: boolean
    is_detaxed: boolean
    is_intragroup: boolean
    has_ongoing_premium_warranty: boolean
    is_paid: boolean
    has_inconsistent_carrier: boolean
    total_price_vat_included: string
    total_price_vat_excluded: string
    total_accepted_amount: string
    total_remitted_amount: string
    vat_rate: null | string
    ecotax_price: string
    shipping_price: null | string
    pickup_store_id: null | number
    warehouse_id: null | number
    carrier_id: null | number
    shipment_method_id: null | number
    max_delivery_date: null | string
    confirm_email_date: null | string
    sms_date: null | string
    store_label: null | string
    billing_address: CustomerOrderAddress
    shipping_address: CustomerOrderAddress
    aggregated_products: object[]
    payments: CustomerOrderPayment[]
    delivery_note_ids: number[]
    tags: Array<{ [key: string]: any }>
}

interface CustomerOrderAddress {
    city: string
    phone: string | null
    email: string
    address: string
    civility: string
    cellphone: string
    lastname: string
    firstname: string
    postal_code: string
    company_name: string
    country_code: string
}

export interface CustomerOrderDetail {
    customer_order_id: number
    customer_id: number
    created_at: Date
    modified_at: null | Date
    statuses: null | string
    amount_all_tax_included: number
    carrier_name: null | string
    store_label: null | number
    origin: null | string
    original_customer_id: number
    payment_fraud_detection: null | string | boolean
    source: string
    source_group: string
    computed_customer_firstname: string
    computed_customer_lastname: string
    invoices: null | []
    payments: CustomerOrderPayment[]
    articles: CustomerOrderArticle[]
    delivery_notes: CustomerOrderDeliveryNote[]
    last_internal_comment: CustomerOrderComment[]
    last_customer_comment: CustomerOrderComment[]
}

export interface CustomerOrderArticle {
    customer_order_id: number
    article_id: number
    sku: null | string
    quantity: null | number
    available_quantity: null | number
    short_description: null | string
    article_image: null | string
}

export interface CustomerOrderDeliveryNote {
    customer_order_id: number
    delivery_note_id: number
    carrier_id: number
    carrier_name: string
    shipment_method_id: number
    shipment_method_name: null | string
    validated_at: null | string
    is_prepared: boolean
    parcel_tracking_number: null | string
    store_pickup_started_at: null | string
    workflow_status: null | string
}

export interface CustomerOrderComment {
    customer_order_id: number
    created_by: string
    created_at: Date
    message: string
}
