<template>
    <tr data-context="table-row-product">
        <!-- Warranty is not reorderable -->
        <td class="py-2 pl-3 pr-0 text-sm w-px whitespace-nowrap align-top"></td>

        <!-- py-4 pl-3 pr-0 whitespace-nowrap text-sm font-medium text-gray-900 -->
        <td :class="getCellClasses('article')" data-context="cell-label" colspan="2">
            <div class="flex items-center gap-3 ml-10">
                <font-awesome-icon
                    fixed-width
                    :icon="['fad', 'shield-check']"
                    class="text-2xl"
                    :class="{ 'text-green-600': is_selected }"
                    :data-context="[is_selected ? 'warranty-is-active' : 'warranty-is-inactive']"
                />
                <span :class="{ 'text-green-900': is_selected }">{{ payload.warranty.label }}</span>
            </div>
        </td>
        <td :class="getCellClasses('stock')" class="bg-gray-50 font-medium" data-context="cell-stock">-</td>
        <td :class="getCellClasses('quantity')" data-context="cell-quantity">
            <erp-tooltip :message="computed_button_attrs.tooltip">
                <erp-switch-selector
                    v-bind="computed_button_attrs"
                    :value="is_selected"
                    :options="[
                        { value: false, icon: ['fad', 'ban'], iconActiveStyle: 'text-red-600' },
                        { value: true, icon: ['fad', 'shopping-cart'], iconActiveStyle: 'text-blue-500' },
                    ]"
                    only-show-icons
                    @input="setSelectedWarranty"
                />
            </erp-tooltip>
        </td>
        <td
            :class="getCellClasses('unit_selling_price_tax_included')"
            data-context="cell-unit-selling-price-tax-included"
        >
            <span>{{ formatCurrency(payload.warranty.unit_selling_price_tax_included) }}</span>
        </td>
        <td :class="getCellClasses('unit_discount_tax_included')" data-context="cell-unit-discount-tax-included">-</td>
        <td :class="getCellClasses('discount_percent')" data-context="cell-discount-percent">-</td>
        <td :class="getCellClasses('discount_total')" data-context="cell-discount-total">-</td>
        <td :class="getCellClasses('selling_price_tax_included')" data-context="cell-selling-price-tax-included">
            {{ is_selected ? formatCurrency(payload.warranty.unit_selling_price_tax_included * quantity) : '-' }}
        </td>
        <td :class="getCellClasses('actions')" class="pr-3" data-context="cell-actions"></td>
    </tr>
</template>

<script setup lang="ts">
import ErpSwitchSelector from '@/shared/components/ui/ErpSwitchSelector.vue'
import { useQuote } from '@/shared/composable/useQuote'
import { formatCurrency } from '@/shared/string_utils'
import { computed } from 'vue'
import ErpTooltip from '@/shared/components/ui/ErpTooltip.vue'

interface Warranty {
    type: string
    unit_selling_price_tax_included: number
    duration: number
    label: string
}

interface LineQuoteProduct {
    delay: number
    discount_percent: number
    margin_all_tax_included: number
    margin_all_tax_included_in_percent: number
    product: any
    product_id: number
    product_quantity: number
    quantity: number
    quote_line_id: number
    quote_line_product_id: number
    status: string
    stock: number
    total_discount_amount: number
    total_selling_price: number
    unit_discount_amount: number
    selected_warranties: Warranty[]
    warranty: Warranty
}

interface Column {
    name: string
    _columnClasses?: string
    _cellClasses?: string
}

interface Props {
    payload: LineQuoteProduct
    quoteId: number
    columns: Column[]
    styles?: { [key: string]: string }
    cellClasses?: { [key: string]: string }
}
const props = defineProps<Props>()

const { is_loading_quote_line, computed_button_attributes, updateSelectedWarranty } = useQuote()

const computed_button_attrs = computed(() =>
    Object.assign({}, { disabled: is_loading_quote_line.value }, computed_button_attributes.value),
)

//
// Selected logic
//
const is_selected = computed(() =>
    props.payload.selected_warranties.some(
        (w) => w.type === props.payload.warranty.type && w.duration === props.payload.warranty.duration,
    ),
)

const setSelectedWarranty = (selected: boolean) => {
    if (is_selected.value === selected) {
        return
    }

    updateSelectedWarranty(props.payload.warranty, props.payload.product_id, selected)
}

//
// Quantity logic
//
const quantity = computed(() => (is_selected.value ? props.payload.product_quantity : 0))

//
// Style logic
//
const column_classes = computed(() => {
    const config: { [key: string]: string } = {}
    props.columns.forEach((column) => {
        if ('_columnClasses' in column && column._columnClasses) {
            config[column.name] = column._columnClasses
        }
    })

    return config
})

const getCellClasses = (column_name: string) => {
    return [
        props.styles?.tbody_td,
        props.styles?.tbody_td_normal,
        props.styles?.padding_default,
        column_classes.value?.[column_name] || '',
        'align-top leading-10',
    ]
}
</script>
