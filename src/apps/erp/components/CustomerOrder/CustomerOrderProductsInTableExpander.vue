<template>
    <tr v-if="articles.length > 0 && false === isLoading" class="border-top-0" data-context="expanded-row">
        <td class="text-sm" :colspan="colspan">
            <div class="flex flex-wrap pr-3 pl-3">
                <div
                    v-for="(article, index) of articles"
                    :key="`${JSON.stringify(article)}-${index}`"
                    class="font-light mr-3 mb-1"
                    data-context="embedded-articles"
                >
                    <span class="font-medium" data-context="article-qty">{{ article.quantity }}</span> x
                    <router-link
                        :class="getCssClasses(article)"
                        :to="`/articles/${article.sku}`"
                        data-context="article-link"
                        target="_blank"
                    >
                        {{ article.short_description }}
                    </router-link>
                </div>
            </div>
        </td>
    </tr>
</template>

<script>
export default {
    name: 'CustomerOrderProductsInTableExpander',
    props: {
        articles: {
            type: Array,
            required: true,
        },
        colspan: {
            type: Number,
            required: true,
        },
        isLoading: {
            type: Boolean,
            required: false,
        },
        sku: {
            type: String,
            default: null,
        },
    },
    methods: {
        getCssClasses(article) {
            const classes = []
            if (article.quantity > article.available_quantity) {
                classes.push('text-red-600')
            }
            if (article.sku === this.sku) {
                classes.push('font-bold')
            }

            return classes.join(' ')
        },
    },
}
</script>
