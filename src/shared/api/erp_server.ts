import { callApi } from '@/services/functions/api'
import type { PagerForErpServer } from '@/shared/composable/usePager/usePager'
import type { Quote } from '../composable/quote.type'
import type { ApiData, ApiFilter, ApiPagerData, ApiRequest } from './type'

export const anonymizeCustomerById = (id: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer/anonymize/${id}`,
    })
}

export const cGetCustomerBoughtProducts = (customer_id: any, params: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer/${customer_id}/bought-products`,
        config: { params },
    })
}

export const cGetAccountSellerCommissionInfos = () => {
    return callApi<
        ApiData<{
            roles: string[]
            role_levels: string[]
        }>
    >('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/account/seller-commission-infos`,
    })
}

export const cGetProductLocations = (product_id: any, params: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/product/${product_id}/locations`,
        config: { params },
    })
}

export const cGetReleaseNotes = (params: PagerForErpServer) => {
    return callApi<
        ApiPagerData<{
            release_notes: {
                release_note_id: string
                type: string
                status: string
                released_at: string
                title: string
                description: string
                notes: string[]
                extracted_notes: string[]
                tags: string[]
            }
        }>
    >('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/releases`,
        config: { params },
    })
}

export const cGetSellerCommissionCommissions = (params: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/seller-commission/commissions`,
        config: { params },
    })
}

export const cGetSellerCommissionMyCommissions = (params: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/seller-commission/my-commissions`,
        config: { params },
    })
}

export const cPostAfterSaleServices = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/after-sale-services`,
        data: params,
    })
}

export const cPostArticles = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/articles`,
        data: params,
    })
}

export const cPostCustomerMessages = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer-messages`,
        data: params,
    })
}

export interface CustomerOrderProduct {
    customer_order_product_id: number
    customer_order_id: number
    customer_id: number
    customer_order_created_at: string
    customer_order_closed_at: string | null
    article_id: number
    sku: string
    article_name: string
    brand_name: string
    article_image: string
    tax_rate: number
    quantity: number
    selling_price_tax_included: number
    buy_price: number
    description: string
    ecotax_price: number
    extension_warranty_duration: number | null
    extension_warranty_price: number
    extension_warranty_tax: number
    extension_warranty_commission: number
    extension_warranty_commission_tax: number
    extension_warranty_seller: string | null
    damage_and_theft_warranty_duration: number | null
    damage_and_theft_warranty_price: number
    damage_and_theft_warranty_commission: number
    damage_and_theft_warranty_commisision_tax: number
    damage_and_theft_warranty_seller: string | null
    discount_type: string | null
    discount_amount: number
    discount_description: string | null
    delivery_note_id: number | null
    margin_tax_included: number
    margin_details: {
        selling_price_tax_included: number
        buy_price_tax_excluded: number
        sorecop_price_tax_included: number
        ecotax_price_tax_included: number
        margin_rate: number
    }[]
}

export const cPostCustomerOrderProducts = (params: any) => {
    return callApi<ApiData<{ customer_order_products: CustomerOrderProduct[] }>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer-order-products`,
        data: params,
    })
}

export const cPostCustomerOrderUrgentDelivery = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer-order/urgent`,
        data: params,
    })
}

export const cPostCustomerOrderWithStorePickup = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer-orders/with-store-pickup`,
        data: params,
    })
}

export const cPostCustomers = (params: any, version = 'v1') => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/${version}/customers`,
        data: params,
    })
}

export const cPostDeliveryNotes = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/delivery-notes`,
        data: params,
    })
}

export const cPostInventories = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/inventories`,
        data: params,
    })
}

export const cPostInventoryProducts = (inventory_id: any, params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/inventory/${inventory_id}/products`,
        data: params,
    })
}

export const cPostInventoryZones = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/inventory/zones`,
        data: params,
    })
}

export const cPostLocations = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/locations`,
        data: params,
    })
}

export const cPostMarketplaceCategories = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/marketplace-categories`,
        data: params,
    })
}

export const cPostMoveMissions = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/move-missions`,
        data: params,
    })
}

export const cPostNewsletterSubscriptionDetails = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/newsletter-subscription-details`,
        data: params,
    })
}

export const cPostPaymentMethods = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v2/payment_methods`,
        data: params,
    })
}

export const cPostQuotes = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quotes`,
        data: params,
    })
}

export const cPostQuoteSubtype = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote-subtype`,
        data: params,
    })
}

export interface ShipmentDeliveryNote {
    shipment_delivery_note_id: number
    delivery_note_id: number
    parcel_weight: number
    shipment_method_id: number
    shipment_method_name: string
    parcel_quantity: number
    parcels: { id: number; number: string }[]
    status: number
    have_insurance: boolean
    customer: {
        city: string
        email: string
        phone: string
        address: [string, string, string, string]
        company: string
        country: string
        civility: string
        lastname: string
        cellphone: string
        firstname: string
        country_id: number
        postal_code: string
    }
}
export const cPostShipmentDeliveryNotes = (shipment_id: number, params: ApiRequest) => {
    return callApi<ApiPagerData<{ shipment_delivery_notes: ShipmentDeliveryNote[] }>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/shipment/${shipment_id}/delivery-notes`,
        data: params,
    })
}

export interface ShipmentMethod {
    shipment_method_id: number
    carrier_id: number
    code: string
    label: string
    is_active: boolean
    comment: string
    type: string
    is_mono_parcel: true
    store_pickup_id?: number
    tags?: any
    carrier?: any
}

export const cPostShipmentMethods = (params: ApiRequest) => {
    return callApi<ApiPagerData<{ shipment_methods: ShipmentMethod[] }>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/shipment-methods`,
        data: params,
    })
}

export const cPostStockMoves = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/stock-moves`,
        data: params,
    })
}

export interface Shipment {
    shipment_id: number
    carrier_id: number
    carrier_name: string
    shipment_account: string
    slip_number: number
    created_at: string
    status: number
    parcel_quantity: number
    closed_at?: string
    shipment_delivery_notes?: {
        delivery_note_id: number
        status: number
        have_all_tracking_numbers: boolean
    }[]
}
export const cPostShipments = (params: ApiRequest) => {
    return callApi<ApiPagerData<{ shipments: Shipment[] }>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/shipments`,
        data: params,
    })
}

export interface UnavailableProduct {
    date: string
    sku_number: number
    total_quantity: number
    turnover: number
}
export const cPostStatisticsUnavailableProducts = (params: ApiFilter) => {
    return callApi<ApiData<{ statistics: UnavailableProduct[] }, { filters: ApiFilter }>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/statistics/unavailable-products`,
        data: { filters: params },
    })
}

export const cPostSubcategoryTypes = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/subcategory-types`,
        data: params,
    })
}

export const cPostSupplierOrderArticles = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/supplier-order-products`,
        data: params,
    })
}

export const cPostSupplierOrders = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/supplier-orders`,
        data: params,
    })
}

export const cPostTasks = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/tasks`,
        data: params,
    })
}

export const cPostWarehouses = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v2/wms/warehouses`,
        data: params,
    })
}

export const cPostWarrantyTypes = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/warranty-types`,
        data: params,
    })
}

export const createTransfer = (params: { warehouse_from: number; warehouse_to: number; products: any }) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/transfer`,
        data: params,
    })
}

export const deleteCustomerAddress = (customer_id: any, index: any) => {
    return callApi<ApiData<any>>('delete', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer/${customer_id}/address/${index}`,
    })
}

export const deleteInventory = (warehouse_id: any) => {
    return callApi<ApiData<any>>('delete', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/inventory/${warehouse_id}`,
    })
}

export const deleteInventoryZone = (inventory_id: any, zone_id: any) => {
    return callApi<ApiData<any>>('delete', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/inventory/${inventory_id}/zone/${zone_id}`,
    })
}

export const deleteQuoteLine = (quote_id: any, quote_line_id: any) => {
    return callApi<ApiData<any>>('delete', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote/${quote_id}/quote-line/${quote_line_id}`,
    })
}

export const deleteSupplierOrderExpectedDelivery = (expected_delivery_id: any) => {
    return callApi<ApiData<any>>('delete', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/expected-delivery/${expected_delivery_id}`,
    })
}

export const deleteWarehouseUser = (warehouse_id: any, user_id: any) => {
    return callApi<ApiData<any>>('delete', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/warehouse/${warehouse_id}/user/${user_id}`,
    })
}

export const detachDeliveryNote = (delivery_note_id: number) => {
    return callApi<ApiData<any>>('delete', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/delivery-note/${delivery_note_id}/detach`,
    })
}

export const deleteSystemEvent = (event_id: number | string) => {
    return callApi<ApiData<any>>('delete', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/system-event/${event_id}`,
    })
}

export const fetchProductStock = (product_id: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/product/${product_id}/stock`,
    })
}

export const fetchWarehouses = () => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/warehouses`,
    })
}

export const getArticleByIdOrSku = (id_or_sku: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/article/${id_or_sku}`,
    })
}

export type CustomerOrderSource = { tag_id: string; source: string; source_group: string }
export const getCustomerOrderSources = () => {
    return callApi<ApiData<{ sources: CustomerOrderSource[] }>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer-order/sources`,
    })
}

export const deleteArticleMediaFile = (filename: any) => {
    return callApi<ApiData<any>>('delete', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/article/media/${filename}`,
    })
}

export const getCustomerById = (id: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer/${id}`,
    })
}

export const getCustomerCreditNotes = (customer_id: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer/${customer_id}/credit-notes`,
    })
}

export const getDeliveryNote = (delivery_note_id: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/delivery-note/${delivery_note_id}`,
    })
}

export const getDeliveryNoteActivity = (delivery_note_id: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/delivery-note/${delivery_note_id}/activity`,
    })
}

export const getDeliveryNoteTracking = (delivery_note_id: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/delivery-note/${delivery_note_id}/tracking`,
    })
}

export const getEasyloungeCustomer = (customer_email: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/easy-lounge/customer/${customer_email}`,
    })
}

export const getEasyloungeInvoice = (customer_order_id: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/easy-lounge/customer-order/${customer_order_id}/invoices`,
    })
}

export const getEasyloungeQuotation = (quotation_id: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/easy-lounge/quotation/${quotation_id}`,
    })
}

export const getInventoryCanClose = (inventory_id: number) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/inventory/${inventory_id}/can-close`,
    })
}

export const getInventoryReport = (inventory_id: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/inventory/${inventory_id}/report`,
    })
}

export const getMailjetMessage = (message_id: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/mailjet/message/${message_id}`,
    })
}

export const getNbToResetPrinting = () => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/nb_to_reset_printing`,
    })
}

export const getQuoteEvents = (quote_id: any, type: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote/${quote_id}/events?type=${type}`,
    })
}

export const getCustomerEvents = (customer_id: any, type: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer/${customer_id}/events?type=${type}`,
    })
}

export const getQuoteShipmentMethods = (quote_id: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote/${quote_id}/shipment-methods`,
    })
}

export const getSystemAdministrationCmsParameters = (path: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/system-administration/cms-parameters/${path}`,
    })
}

export const getWarehouseStartedStorePickups = (warehouse_id: any) => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/warehouse/${warehouse_id}/started-store-pickups`,
    })
}

export const postAskChangeEmailCustomer = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer/ask-change-email`,
        data: params,
    })
}

export const postCloneQuote = (quote_id: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote/${quote_id}/clone`,
    })
}

export const postCreateCustomer = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer`,
        data: params,
    })
}

export const postCustomerAddress = (customer_id: any, params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer/${customer_id}/address`,
        data: params,
    })
}

export const postCustomerMessageReply = (customer_message_id: any, params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer-message/${customer_message_id}/reply`,
        data: params,
    })
}

export const postDeliveryNoteAbortPicking = (delivery_note_id: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/delivery-note/${delivery_note_id}/abort-picking`,
    })
}

export const postDeliveryNoteGenerate = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/delivery-note/generate`,
        data: params,
    })
}

export const postInventory = (warehouse_id: any, params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/inventory/${warehouse_id}`,
        data: params,
    })
}

export const postInventoryArticleCount = (inventory_id: any, product_id: any, params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/inventory/${inventory_id}/product/${product_id}/count`,
        data: params,
    })
}

export const postInventoryZone = (inventory_id: any, zone_id: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/inventory/${inventory_id}/zone/${zone_id}`,
    })
}

export const postMetabaseEmbedUrl = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/metabase/embed-dashboard-url`,
        data: params,
    })
}

export const postNewsletterSubscriptionChangeStatus = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/newsletter-subscription-change-status`,
        data: params,
    })
}

export const postProductLocationUnlink = (product_id: any, location_id: any, data: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/product/${product_id}/location/${location_id}/unlink`,
        data: data,
    })
}

export const postQuote = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote`,
        data: params,
    })
}

export const postQuoteInternalComment = (quote_id: any, params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote/${quote_id}/internal-comment`,
        data: { data: params },
    })
}

export const postCustomerInternalComment = (customer_id: any, message: string) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer/${customer_id}/internal-comment`,
        data: { message },
    })
}

export const postQuoteLineProduct = (quote_id: any, sku: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote/${quote_id}/quote-line-product`,
        data: { sku },
    })
}

export const postQuoteToCustomerOrder = (quote_id: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote/${quote_id}/customer-order`,
    })
}

export const postSalesPeriod = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/sales-period`,
        data: params,
    })
}

export const postSendQuoteEmail = (quote_id: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote/${quote_id}/send-email`,
    })
}

export const postSupplierOrder = (params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/supplier-order`,
        data: params,
    })
}

export const postSupplierOrderExpectedDeliveries = (supplier_order_id: any, params: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/supplier-order/${supplier_order_id}/expected-deliveries`,
        data: params,
    })
}

export const postSynchronizeSubcategory = (subcategory_id: any, use_filters: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/eav/subcategory/${subcategory_id}/synchronize`,
        data: {
            use_filters,
        },
    })
}

export const postSystemAdministrationCmsParameters = (parameters: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/system-administration/cms-parameters`,
        data: {
            parameters,
        },
    })
}

export const postSystemAffiliationConsolidation = (file_path: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer-order/affiliation-consolidation`,
        data: {
            file_path,
        },
    })
}

export const postTemporaryFileUpload = (formData: any) => {
    return callApi<ApiData<any>>('post', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/temporary-file/upload`,
        data: formData,
    })
}

export const putArticleEans = (article_id: any, data: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/article/${article_id}/eans`,
        data,
    })
}

export const putArticleSafetyStockThreshold = (article_id: any, warehouse_id: any, params: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/article/${article_id}/warehouse/${warehouse_id}`,
        data: params,
    })
}

export const putCategory = (category: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/category/${category.category_id}`,
        data: category,
    })
}

export const putCustomerAddress = (customer_id: any, index: any, params: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer/${customer_id}/address/${index}`,
        data: params,
    })
}

export const putDeliveryNote = (delivery_note_id: number, data: any) => {
    return callApi<ApiData<{}>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/delivery-note/${delivery_note_id}`,
        data,
    })
}

export const putDeliveryNoteStartStorePickup = (delivery_note_id: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/delivery-note/${delivery_note_id}/start-store-pickup`,
    })
}

export const putInventory = (inventory_id: any, data: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/inventory/${inventory_id}`,
        data: data,
    })
}

export const putInventoryClose = (inventory_id: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/inventory/${inventory_id}/close`,
    })
}

export const putInventoryCollectActivate = (inventory_id: any, inventory_collect_id: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${
            import.meta.env.VITE_APP_ERP_API
        }/v1/wms/inventory/${inventory_id}/collect/${inventory_collect_id}/activate`,
    })
}

export const putInventoryDifferentialValidationToggle = (inventory_id: any, product_id: any, differential_id: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${
            import.meta.env.VITE_APP_ERP_API
        }/v1/wms/inventory/${inventory_id}/product/${product_id}/toggle-validation-status/${differential_id}`,
    })
}

export const putProductLocation = (product_id: any, location_id: any, data: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/product/${product_id}/location/${location_id}`,
        data: data,
    })
}

export const putQuote = (quote_id: any, params: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote/${quote_id}`,
        data: { data: params },
    })
}

export const putQuoteLine = (quote_id: number, quote_line_id: number, params: { display_position: number }) => {
    return callApi<ApiData<{ quote: Quote }>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote/${quote_id}/quote-line/${quote_line_id}`,
        data: params,
    })
}

export const putQuoteLineProduct = (quote_id: any, quote_line_product_id: any, params: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote/${quote_id}/quote-line-product/${quote_line_product_id}`,
        data: { data: params },
    })
}

export const putQuoteAllocateDiscountGlobally = (quote_id: any, amount: any, mode = 'DRY_RUN') => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/quote/${quote_id}/allocate-discount-globally`,
        data: { amount, mode },
    })
}

export const putCloseShipment = (shipment_id: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/shipment/${shipment_id}/close`,
    })
}

export const putStockMove = (stock_move_id: any, params: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/stock-move/${stock_move_id}`,
        data: params,
    })
}

export const putSupplierOrderExpectedDelivery = (expected_delivery_id: any, quantity: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/expected-delivery/${expected_delivery_id}`,
        data: { quantity: quantity },
    })
}

export const putSupplierOrderProduct = (supplier_order_id: any, params: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/supplier-order/${supplier_order_id}/product`,
        data: params,
    })
}

export const putUpdateEmailCustomer = (params: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/customer/email`,
        data: params,
    })
}

export const putWarehouseUser = (warehouse_id: any, user_id: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/warehouse/${warehouse_id}/user/${user_id}`,
    })
}

export const putWmsParcelNumber = (parcel_id: number, parcel_number: string) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/parcel/${parcel_id}`,
        data: { parcel_number },
    })
}

export const putWmsMoveMissionAssignee = (move_mission_id: any, assignee_id: any) => {
    return callApi<ApiData<any>>('put', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/wms/move-mission/${move_mission_id}/assign/${assignee_id}`,
    })
}

export const resetPrint = () => {
    return callApi<ApiData<any>>('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/reset_printing`,
    })
}

export const fetchCommonContentBySku: any = (sku: any) => {
    return callApi('get', {
        url: `${import.meta.env.VITE_APP_ERP_API}/v1/common-content/${sku}`,
    })
}
