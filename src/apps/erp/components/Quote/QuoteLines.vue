<script setup>
import { ErpTable, ErpButton } from '@/shared/components'
import QuoteLineProductItem from '@/apps/erp/components/Quote/QuoteLineProductItem.vue'
import QuoteLineProductWarrantyItem from '@/apps/erp/components/Quote/QuoteLineProductWarrantyItem.vue'
import { TYPE_PRODUCT, TYPE_PRODUCT_WARRANTY, updateQuoteLinePosition, useQuote } from '@/shared/composable/useQuote'
import { computed, ref } from 'vue'
import ArticleAutocomplete from '@/shared/components/autocomplete/ArticleAutocomplete.vue'
import QuoteShippingInfo from '@/apps/erp/components/Quote/QuoteShippingInfo.vue'
import QuoteSubtypeSelector from '@/apps/erp/components/Quote/QuoteSubtypeSelector.vue'
import QuoteLinesFooter from '@/apps/erp/components/Quote/QuoteLinesFooter.vue'
import TransferQuote from '@/apps/erp/components/Quote/TransferQuote.vue'
import ErpTooltip from '@/shared/components/ui/ErpTooltip.vue'
import ErpInputGroup from '@/shared/components/form/ErpInputGroup.vue'
import ErpFakeInput from '@/shared/components/form/ErpFakeInput.vue'
import ErpMultiselect from '@/shared/components/form/ErpMultiselect.vue'
import { faStream } from '@son-video/font-awesome-pro-solid/faStream'
import draggable from 'vuedraggable'
import SlideOutContainer from '@/shared/components/ui/SlideOutContainer.vue'

const {
    quote,
    can_edit,
    is_locked,
    is_loading,
    is_loading_quote_line,
    computed_transfer_attributes,
    computed_validity_attributes,
    addProduct,
    updateQuote,
} = useQuote()

const is_transfer_open = ref(false)

const ITEMS_TYPES = {
    product: QuoteLineProductItem,
    'product-warranty': QuoteLineProductWarrantyItem,
}

const valid_until_options = computed(() => {
    let options = []
    for (let value = 1; value <= 30; value++) {
        options.push({ value, label: `${value} jour${value > 1 ? 's' : ''}` })
    }
    return options
})

const selected_valid_until = computed(() =>
    valid_until_options.value.filter((vuo) => vuo.value === quote.value.valid_until),
)

const columns = ref([
    { name: 'reorder' },
    { name: 'article' },
    { name: 'budget_promo', title: '' },
    {
        name: 'stock',
        _columnClasses: 'pr-3 text-right w-px whitespace-nowrap',
        _cellClasses: 'leading-10',
    },
    {
        name: 'quantity',
        title: 'Quantité',
        _columnClasses: 'text-right w-px whitespace-nowrap',
        _cellClasses: 'leading-10',
    },
    {
        name: 'unit_selling_price_tax_included',
        title: 'Prix Un. TTC',
        _columnClasses: 'text-right w-px whitespace-nowrap',
        _cellClasses: 'leading-10',
    },
    {
        name: 'unit_discount_tax_included',
        title: 'Remise Un. TTC',
        _columnClasses: 'text-right w-px whitespace-nowrap',
        _cellClasses: 'leading-10',
    },
    {
        name: 'discount_percent',
        title: 'Remise %',
        _columnClasses: 'text-right w-px whitespace-nowrap',
        _cellClasses: 'leading-10',
    },
    {
        name: 'discount_total',
        title: 'Remise Totale',
        _columnClasses: 'text-right w-px whitespace-nowrap',
        _cellClasses: 'leading-10',
    },
    {
        name: 'selling_price_tax_included',
        title: 'Prix TTC',
        _columnClasses: 'text-right w-px whitespace-nowrap',
        _cellClasses: 'leading-10',
    },
    { name: 'actions', title: ' ' },
])

const can_move = ref(false)

const quote_lines = computed(() =>
    quote.value.quote_line_aggregates
        .filter((quote_line) => quote_line.type !== TYPE_PRODUCT_WARRANTY)
        .reduce((stack, current) => {
            stack.push(current)

            if (TYPE_PRODUCT === current.type && !can_move.value) {
                ;(current.product?.eligible_warranties ?? []).forEach((eligible_warranty) => {
                    stack.push({
                        type: TYPE_PRODUCT_WARRANTY,
                        warranty: eligible_warranty,
                        quote_line_product_id: current.quote_line_product_id,
                        product_quantity: current.quantity,
                        product_id: current.product_id,
                        selected_warranties: current.selected_warranties,
                    })
                })
            }

            return stack
        }, []),
)

const stopMove = () => {
    can_move.value = false
}

const onMoveEnd = (event) => {
    updateQuoteLinePosition(event.oldDraggableIndex, event.newDraggableIndex)
}
</script>

<template>
    <div class="rounded bg-white border border-gray-200 mx-3 shadow-sm">
        <!-- Action bar -->
        <div class="p-3 flex space-x-3 border-b">
            <quote-subtype-selector
                :value="quote.quote_subtype"
                @input="updateQuote(quote.quote_id, { quote_subtype: $event })"
            />

            <erp-tooltip :message="computed_validity_attributes.tooltip">
                <erp-input-group prefix="Validité" data-context="valid-until">
                    <erp-multiselect
                        class="min-w-[80px]"
                        :value="selected_valid_until"
                        :options="valid_until_options"
                        v-bind="computed_validity_attributes"
                        :is-loading="is_loading"
                        with-select-presets
                        @select="updateQuote(quote.quote_id, { valid_until: $event.value })"
                    />
                </erp-input-group>
            </erp-tooltip>

            <erp-input-group prefix="Créé par" data-context="created_by">
                <erp-fake-input class="font-medium">
                    {{ quote.created_by_name }}
                </erp-fake-input>
            </erp-input-group>

            <erp-tooltip :message="computed_transfer_attributes.tooltip">
                <erp-button
                    tertiary
                    :icon="['fad', 'user-edit']"
                    :icon-class="['text-blue-600']"
                    data-context="transfer-button"
                    v-bind="computed_transfer_attributes"
                    @click.prevent.stop="is_transfer_open = true"
                />
            </erp-tooltip>
        </div>

        <!-- Table -->
        <erp-table
            :columns="columns"
            :rows="quote_lines"
            no-border
            no-presets
            class="rounded"
            :styles="{ padding_default: 'pl-3' }"
        >
            <!-- Override first column with a sort icon  or a loader when necessary -->
            <template #header__reorder>
                <erp-tooltip :message="`${!can_move ? 'Activer' : 'Désactiver'} le déplacement`">
                    <erp-button
                        type="button"
                        tertiary
                        :icon="faStream"
                        :is-loading="is_loading_quote_line"
                        :icon-class="{ 'text-blue-600': can_move }"
                        :disabled="is_locked"
                        data-context="drag-and-drop-button"
                        v-bind="computed_transfer_attributes"
                        @click="can_move = !can_move"
                    />
                </erp-tooltip>
            </template>

            <!-- Rows: overrides default ErpTable behaviour -->

            <template #tbody="{ rows, rowClasses, styles, cellClasses }">
                <draggable
                    :value="rows"
                    tag="tbody"
                    draggable="[data-context=table-row-product]"
                    handle="[data-context=quote-reorder-handle]"
                    @end="onMoveEnd($event)"
                >
                    <component
                        :is="ITEMS_TYPES[row.type]"
                        v-for="(row, rowIndex) in rows"
                        :key="row.quote_line_id"
                        :payload="row"
                        :quote-id="quote.quote_id"
                        :cell-classes="cellClasses"
                        :styles="styles"
                        :columns="columns"
                        :class="rowClasses[rowIndex]"
                        :can-move="can_move"
                        @focus="stopMove()"
                    />
                </draggable>
            </template>

            <!-- Shown when there no product or section added yet -->
            <template #no_result_content>
                <div class="flex flex-col pt-3 pb-2">
                    <div class="pb-2">
                        <font-awesome-icon class="text-blue-600" :icon="['fad', 'cart-plus']" size="3x" />
                    </div>
                    <span class="font-medium text-base text-slate-600">Aucun produit ou section</span>
                    <span class="text-slate-400">Ajoutez-en à l'aide du champs ci-dessous.</span>
                </div>
            </template>

            <!-- Fixed row: Add product or section -->
            <template #last_row>
                <tr v-if="can_edit && false === is_locked">
                    <td class="px-3 py-4 bg-gray-50" colspan="11" data-context="add-product-or-section">
                        <article-autocomplete
                            placeholder="Ajouter un produit ou une section..."
                            context="quote"
                            @select="addProduct($event.sku)"
                        >
                            <!-- TODO : UNCOMMENT WHEN ADDING SECTION-->
                            <!-- <template #below="{ suggestions, search_terms }">-->
                            <!--     &lt;!&ndash; Add a new section &ndash;&gt;-->
                            <!--     <div-->
                            <!--         class="px-4 py-4 text-sm bg-gray-50 hover:bg-gray-100 cursor-pointer"-->
                            <!--         :class="{-->
                            <!--             'border-t': suggestions.length > 0,-->
                            <!--         }"-->
                            <!--     >-->
                            <!--         <font-awesome-icon :icon="['fas', 'plus']" fixed-width class="mr-2 h-3" />-->
                            <!--         Ajouter-->
                            <!--         <span class="bg-yellow-200 py-1 px-2 mx-1 font-medium">{{ search_terms }}</span>-->
                            <!--         en tant que titre de section-->
                            <!--     </div>-->
                            <!-- </template>-->
                        </article-autocomplete>
                    </td>
                </tr>

                <quote-shipping-info />

                <quote-lines-footer />
            </template>
        </erp-table>
        <slide-out-container :is-open="is_transfer_open" @close="is_transfer_open = false">
            <transfer-quote @close="is_transfer_open = false" />
        </slide-out-container>
    </div>
</template>
