# Tests pour CustomerOrderProductsMargin

Ce document décrit le test e2e créé pour le composant `CustomerOrderProductsMargin.vue`, basé sur la structure de `customer_order_products_availability_statuses.e2e.js`.

## Fichier de test

### Test e2e Cypress
**Fichier** : `tests/e2e/specs/customer_order/customer_order_products_margin.e2e.js`

**Description** : Tests end-to-end utilisant Cypress pour tester l'affichage des marges produits dans le contexte complet de l'application.

**Structure** : Basé sur `customer_order_products_availability_statuses.e2e.js` avec la même approche modulaire.

## Couverture des tests

### Tests principaux
- ✅ **Affichage général** : Panneau latéral, en-têtes, structure du tableau
- ✅ **Premier produit** : S809 HCS Bois noir (quantité 2, calculs détaillés)
- ✅ **Deuxième produit** : S810 SUB Bois noir (quantité 1, calculs détaillés)
- ✅ **Troisième produit** : Câble HDMI 2m (quantité 3, calculs détaillés)
- ✅ **Ligne de total** : Calculs agrégés et style (gras, couleur)
- ✅ **Formatage des devises** : Format français avec virgule et €
- ✅ **Gestion d'erreur** : API 500 avec toast d'erreur
- ✅ **État vide** : Aucun produit dans la commande

### Détails par produit testés
Pour chaque produit, le test vérifie :
- **SKU** avec lien vers la fiche article
- **Nom** du produit
- **Image** avec URL correcte
- **Quantité** commandée
- **Prix de vente TTC** calculé (prix unitaire × quantité)
- **Remise TTC** appliquée
- **Marge TTC** calculée (marge unitaire × quantité)

## Fixtures utilisées

### Fixture principale
**Fichier** : `tests/e2e/fixtures/erp/customer-order-products/customer_order_products_margin.json`

**Contenu** : 3 produits avec données complètes de marges
- Produit 1 : S809 HCS Bois noir (qty: 2, prix: 299.99€, marge: 95.25€)
- Produit 2 : S810 SUB Bois noir (qty: 1, prix: 199.99€, marge: 65.50€)
- Produit 3 : Câble HDMI 2m (qty: 3, prix: 12.99€, marge: 7.85€)

### Fixture vide
**Fichier** : `tests/e2e/fixtures/erp/customer-order-products/customer_order_products_margin_empty.json`

**Contenu** : Liste vide pour tester l'état sans produits

## Calculs vérifiés

### Calculs par produit
- **Produit 1** : Prix total = 299,99 × 2 = 599,98€, Marge totale = 95,25 × 2 = 190,50€
- **Produit 2** : Prix total = 199,99 × 1 = 199,99€, Marge totale = 65,50 × 1 = 65,50€
- **Produit 3** : Prix total = 12,99 × 3 = 38,97€, Marge totale = 7,85 × 3 = 23,55€

### Totaux agrégés
- **Quantité totale** : 2 + 1 + 3 = 6
- **Prix de vente total** : 599,98 + 199,99 + 38,97 = 838,94€
- **Remise totale** : 15,50 + 8,00 + 2,50 = 26,00€
- **Marge totale** : 190,50 + 65,50 + 23,55 = 279,55€

## Structure du test

### Pattern utilisé (basé sur le fichier de référence)
```javascript
describe('Customer order products margin', () => {
    const VISIT_PAGE = (file) => {
        // Intercepts API
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')
        cy.emitEvent('open-products-margin', 123456)
        cy.wait('@customer_order')
        cy.wait('@cpost_customer_order_products')
    }

    describe('Displays customer order products margin', () => {
        it('displays products margin with correct calculations', () => {
            // Test général
        })
        
        it('displays first product margin details correctly', () => {
            // Test spécifique produit 1
        })
        
        // ... autres tests par produit
    })
})
```

### Avantages de cette approche
1. **Tests modulaires** : Chaque produit testé individuellement
2. **Réutilisabilité** : Fonction `VISIT_PAGE` réutilisée
3. **Lisibilité** : Tests focalisés sur un aspect spécifique
4. **Maintenance** : Facile à étendre pour de nouveaux cas
5. **Cohérence** : Même structure que les autres tests du projet

## Exécution

```bash
npm run e2e tests/e2e/specs/customer_order/customer_order_products_margin.e2e.js
```

## Intégration

### Contexte d'utilisation
- **Page** : `/legacy/v1/commandes/edition_commande.php?id_commande=123456`
- **Événement** : `cy.emitEvent('open-products-margin', 123456)`
- **Panneau** : `[data-context=slide-out-container]`
- **Contenu** : `[data-context=products-availability]`

### APIs interceptées
- `GET **/api/erp/v1/customer-order/123456/for-edition-page` : Données de la commande
- `POST **/api/erp/v1/customer-order-products` : Produits avec détails de marges

## Maintenance

Pour maintenir ce test :
1. **Mettre à jour les fixtures** si la structure API change
2. **Ajouter de nouveaux produits** en suivant le même pattern
3. **Vérifier les calculs** si la logique métier évolue
4. **Maintenir la cohérence** avec les autres tests e2e du projet
5. **Documenter les changements** dans ce fichier
