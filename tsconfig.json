{
    // @see https://github.com/vuejs/tsconfig
    // uncomment when cypress is fixed https://github.com/cypress-io/cypress/issues/27466
    // and remove the hard-coded values below
    // "extends": ["@vue/tsconfig/tsconfig.json"],
    "compilerOptions": {
        // start @vue/tsconfig/tsconfig.json
        "module": "ESNext",
        "moduleResolution": "node",
        "resolveJsonModule": true,
        "jsx": "preserve",
        "jsxImportSource": "vue",
        "noImplicitThis": true,
        "strict": true,
        "verbatimModuleSyntax": true,
        "target": "ESNext",
        "useDefineForClassFields": true,
        "esModuleInterop": true,
        "forceConsistentCasingInFileNames": true,
        "skipLibCheck": true,
        // end @vue/tsconfig/tsconfig.json
        "types": ["node", "vite/client", "vitest/globals"],
        "allowJs": true,
        "baseUrl": ".",
        "paths": {
            "@/*": ["./src/*"]
        },
        "sourceMap": false,
        "lib": ["esnext", "dom", "dom.iterable"],
        "plugins": [
            {
                "name": "@vuedx/typescript-plugin-vue"
            }
        ],
        "outDir": "build/out-tsc"
    },
    "files": ["src/@types/graphql.d.ts"],
    "include": ["src/**/*.js", "src/**/*.ts", "src/**/*.vue"],
    "exclude": ["node_modules", "**/*.spec.ts", "**/*.test.ts", "**/*.cy.ts", "**/*.story.vue", "./build/**.*"],
    "vueCompilerOptions": {
        "target": 2.7
    }
}
