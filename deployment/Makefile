PACKAGE_NAME=erp-client
PACKAGE_DIR=$(PACKAGE_NAME)
ZONE_STG=eu-west-2
ZONE_VAL=eu-west-2
ZONE_PRD=eu-central-1

.PHONY: deploy
deploy: deployment.id

.PHONY: deploy-validation
deploy-validation: deployment-validation.id

deployment.id: bump-patch-version $(PACKAGE_NAME)-stg.tar $(PACKAGE_NAME)-prd.tar
	git tag -l "v[0-9]*" | sort --version-sort -r | head -1 | awk -F'[.-]' '/^v/{print $$1"."$$2"."$$3+1}' > version
	tar -uf $(PACKAGE_NAME)-stg.tar version
	tar -uf $(PACKAGE_NAME)-prd.tar version
	gzip $(PACKAGE_NAME)-stg.tar
	gzip $(PACKAGE_NAME)-prd.tar
	aws --output=text --region=$(ZONE_STG) s3 cp \
	    $(PACKAGE_NAME)-stg.tar.gz \
	    s3://svd-deployer-stg/$(PACKAGE_NAME).tar.gz \
	    --metadata "GitVersion=$(shell git rev-parse HEAD)"
	aws --output=text --region=$(ZONE_STG) s3 cp \
	    $(PACKAGE_NAME)-prd.tar.gz \
	    s3://svd-deployer-prd/$(PACKAGE_NAME)-prd.tar.gz \
	    --metadata "GitVersion=$(shell git rev-parse HEAD)"
	$(eval VERSION := $(shell git tag -l "v[0-9]*" | sort --version-sort -r | head -1 | awk -F'[.-]' '/^v/{print $$1"."$$2"."$$3+1}')-staging)
	git tag $(VERSION)
	git push --tags --no-verify
	echo $(VERSION) > deployment.id

.PHONY: bump-patch-version
bump-patch-version:
	$(eval VERSION := $(shell git tag -l "v[0-9]*" | sort --version-sort -r | head -1 | awk -F'[.-]' '/^v/{print $$1"."$$2"."$$3+1}'))
	@sed -i 's|\(.*"version"\): "\(.*\)",.*|\1: '"\"$(VERSION)\",|" ../package.json

deployment-validation.id: bump-patch-version-validation $(PACKAGE_NAME)-val.tar
	git tag -l "validation.[0-9]*" | sort --version-sort -r | head -1 | awk -F'[.]' '/^validation/{print $$1"."$$2+1}' > version
	tar -uf $(PACKAGE_NAME)-val.tar version
	gzip $(PACKAGE_NAME)-val.tar
	aws --output=text --region=$(ZONE_STG) s3 cp \
	    $(PACKAGE_NAME)-val.tar.gz \
	    s3://svd-deployer-val/$(PACKAGE_NAME).tar.gz \
	    --metadata "GitVersion=$(shell git rev-parse HEAD)"
	$(eval VERSION := $(shell git tag -l "validation.[0-9]*" | sort --version-sort -r | head -1 | awk -F'[.]' '/^validation/{print $$1"."$$2+1}'))
	git tag $(VERSION)
	git push --tags --no-verify
	echo $(VERSION) > deployment-validation.id

.PHONY: bump-patch-version-validation
bump-patch-version-validation:
	$(eval VALIDATION_VERSION := $(shell git tag -l "validation.[0-9]*" | sort --version-sort -r | head -1 | awk -F'[.]' '/^validation/{print $$2+1}'))
	$(eval VERSION := $(shell git tag -l "v[0-9]*" | sort --version-sort -r | head -1 | awk -F'[.]' '/^v/{print $$1"."$$2"."$$3}'))
	@sed -i 's|\(.*"version"\): "\(.*\)",.*|\1: '"\"$(VERSION)\-validation\.$(VALIDATION_VERSION)\",|" ../package.json

.PHONY: package
package: $(PACKAGE_NAME)-prd.tar

.PHONY: package-stg
package-stg: $(PACKAGE_NAME)-stg.tar

.PHONY: package-val
package-val: $(PACKAGE_NAME)-val.tar

$(PACKAGE_NAME)-prd.tar: ../node_modules
	@if [ ! -f skip-recipe ]; \
	then \
		cd .. ; cp .env.production.dist .env.production ; \
		sed -i "s/PROD_CLIENT_ID/$(CLIENT_ID)/g" .env.production ; \
		sed -i "s/PROD_CLIENT_SECRET/$(CLIENT_SECRET)/g" .env.production ; \
		npm run build:production ; cd - ; \
		rm -rf $(PACKAGE_DIR) ; \
		mkdir $(PACKAGE_DIR) ; \
		cp -r ../dist/* $(PACKAGE_DIR)/ ; \
		tar cf $(PACKAGE_NAME)-prd.tar --exclude-vcs $(PACKAGE_NAME) ; \
	else \
		printf " $(OK_COLOR)-- [SKIPPING RECIPE]$(NO_COLOR) Skipping npm jobs \n"; \
	fi

$(PACKAGE_NAME)-stg.tar: ../node_modules
	@if [ ! -f skip-recipe ]; \
	then \
		cd .. ; npm run build:staging ; cd - ; \
		rm -rf $(PACKAGE_DIR) ; mkdir $(PACKAGE_DIR) ; \
		cp -r ../dist/* $(PACKAGE_DIR)/ ; \
		tar cf $(PACKAGE_NAME)-stg.tar --exclude-vcs $(PACKAGE_NAME) ; \
	else \
		printf " $(OK_COLOR)-- [SKIPPING RECIPE]$(NO_COLOR) Skipping npm jobs \n"; \
	fi

$(PACKAGE_NAME)-val.tar: ../node_modules
	@if [ ! -f skip-recipe ]; \
	then \
		cd .. ; npm run build:validation ; cd - ; \
		rm -rf $(PACKAGE_DIR) ; mkdir $(PACKAGE_DIR) ; \
		cp -r ../dist/* $(PACKAGE_DIR)/ ; \
		tar cf $(PACKAGE_NAME)-val.tar --exclude-vcs $(PACKAGE_NAME) ; \
	else \
		printf " $(OK_COLOR)-- [SKIPPING RECIPE]$(NO_COLOR) Skipping package creation for validation \n"; \
	fi

../node_modules:
	@if [ ! -f skip-recipe ]; \
	then \
		cd .. ; cp .npmrc.dist .npmrc; sed -i "s/TOKEN/$(FONT_AWESOME_TOKEN)/g" .npmrc ; npm ci --production ; \
	else \
		printf " $(OK_COLOR)-- [SKIPPING RECIPE]$(NO_COLOR) Skipping npm jobs \n"; \
	fi

.PHONY: skip-some-recipes
skip-some-recipes:
	@> skip-recipe

.PHONY: clean
clean:
	rm -rf deployment.id deployment-validation.id $(PACKAGE_NAME)-*.tar $(PACKAGE_NAME)-*.tar.gz version $(PACKAGE_DIR) ../node_modules || exit 0
