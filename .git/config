[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
[remote "origin"]
	url = **************:son-video/erp-client.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "dev"]
	remote = origin
	merge = refs/heads/dev
[branch "sprint_api/release"]
	remote = origin
	merge = refs/heads/sprint_api/release
[branch "feature_api_ezl/release"]
	remote = origin
	merge = refs/heads/feature_api_ezl/release
[branch "feature_api_ezl/redoc"]
	remote = origin
	merge = refs/heads/feature_api_ezl/redoc
[branch "hotfix_analyse_vente/customer_order"]
	remote = origin
	merge = refs/heads/hotfix_analyse_vente/customer_order
[branch "identification_devis/add_subtype"]
	remote = origin
	merge = refs/heads/identification_devis/add_subtype
[branch "identification_devis/release"]
	remote = origin
	merge = refs/heads/identification_devis/release
[branch "identification_devis/subtype_event"]
	remote = origin
	merge = refs/heads/identification_devis/subtype_event
[branch "optiquote/release"]
	remote = origin
	merge = refs/heads/optiquote/release
[branch "optiquote/timeline"]
	remote = origin
	merge = refs/heads/optiquote/timeline
[branch "optiquote/header"]
	remote = origin
	merge = refs/heads/optiquote/header
[branch "identification_devis/fix_permissions"]
	remote = origin
	merge = refs/heads/identification_devis/fix_permissions
[branch "quote/remove_sales_channel"]
	remote = origin
	merge = refs/heads/quote/remove_sales_channel
[branch "quote/release"]
	remote = origin
	merge = refs/heads/quote/release
[branch "quote/wipe_sales_channel"]
	remote = origin
	merge = refs/heads/quote/wipe_sales_channel
[branch "evol_fiche_article/logistic_1350"]
	remote = origin
	merge = refs/heads/evol_fiche_article/logistic_1350
[branch "evol_fiche_article/stock_buyers_1307"]
	remote = origin
	merge = refs/heads/evol_fiche_article/stock_buyers_1307
[branch "evol_fiche_article/buyers_1310"]
	remote = origin
	merge = refs/heads/evol_fiche_article/buyers_1310
[branch "optiquote/issue_1242_new_loader"]
	remote = origin
	merge = refs/heads/optiquote/issue_1242_new_loader
[branch "quote/rewind_order_origin"]
	remote = origin
	merge = refs/heads/quote/rewind_order_origin
[branch "supplier_order/1342_create_supplier_order"]
	remote = origin
	merge = refs/heads/supplier_order/1342_create_supplier_order
[branch "supplier_order/fix_from_validation"]
	remote = origin
	merge = refs/heads/supplier_order/fix_from_validation
[branch "stocktake/1466_add_location"]
	remote = origin
	merge = refs/heads/stocktake/1466_add_location
[branch "stocktake/release"]
	remote = origin
	merge = refs/heads/stocktake/release
[branch "supplier_order/1412_save_filters"]
	remote = origin
	merge = refs/heads/supplier_order/1412_save_filters
[branch "supplier_order/release"]
	remote = origin
	merge = refs/heads/supplier_order/release
[branch "supplier_order/1371_perf_issue"]
	remote = origin
	merge = refs/heads/supplier_order/1371_perf_issue
[branch "seller_commission_v16/display_b2c"]
	remote = origin
	merge = refs/heads/seller_commission_v16/display_b2c
[branch "supplier_order/1342_change_permission_name"]
	remote = origin
	merge = refs/heads/supplier_order/1342_change_permission_name
[branch "supplier_order/1371_fixes"]
	remote = origin
	merge = refs/heads/supplier_order/1371_fixes
[branch "supplier_order/1371_tooltip"]
	remote = origin
	merge = refs/heads/supplier_order/1371_tooltip
[branch "Supplier_order/release"]
	remote = origin
	merge = refs/heads/Supplier_order/release
[branch "inventory/release"]
	remote = origin
	merge = refs/heads/inventory/release
[branch "inventory/1462_auto_validation"]
	remote = origin
	merge = refs/heads/inventory/1462_auto_validation
[branch "partial_inventory/1457_inventory_zones"]
	remote = origin
	merge = refs/heads/partial_inventory/1457_inventory_zones
[branch "partial_inventory/1457_inventory_zones_2"]
	remote = origin
	merge = refs/heads/partial_inventory/1457_inventory_zones_2
[branch "partial_inventory/release"]
	remote = origin
	merge = refs/heads/partial_inventory/release
[branch "partial_inventory/release_2"]
	remote = origin
	merge = refs/heads/partial_inventory/release_2
[branch "partial_inventory/1515_remove_inventory"]
	remote = origin
	merge = refs/heads/partial_inventory/1515_remove_inventory
[branch "partial_inventory/release_3"]
	remote = origin
	merge = refs/heads/partial_inventory/release_3
[branch "partial_inventory/release_3.1"]
	remote = origin
	merge = refs/heads/partial_inventory/release_3.1
[branch "partial_inventory/1556_closing_check"]
	remote = origin
	merge = refs/heads/partial_inventory/1556_closing_check
[branch "partial_inventory/release_4"]
	remote = origin
	merge = refs/heads/partial_inventory/release_4
[branch "supplier_order/release_2"]
	remote = origin
	merge = refs/heads/supplier_order/release_2
[branch "partial_inventory/1556_closing_check_2"]
	remote = origin
	merge = refs/heads/partial_inventory/1556_closing_check_2
[branch "supplier_order/1587_rework_qte_attente"]
	remote = origin
	merge = refs/heads/supplier_order/1587_rework_qte_attente
[branch "deploy/1611_readme"]
	remote = origin
	merge = refs/heads/deploy/1611_readme
[branch "article_v2/1632_pondere"]
	remote = origin
	merge = refs/heads/article_v2/1632_pondere
[branch "article_v2/view_basic_info"]
	remote = origin
	merge = refs/heads/article_v2/view_basic_info
[branch "partial_inventory/create_product_inventory_1693"]
	remote = origin
	merge = refs/heads/partial_inventory/create_product_inventory_1693
[branch "article_v2/release"]
	remote = origin
	merge = refs/heads/article_v2/release
[branch "article_v2/spike_composee"]
	remote = origin
	merge = refs/heads/article_v2/spike_composee
[branch "article_v2/coumpound/1729_header"]
	remote = origin
	merge = refs/heads/article_v2/coumpound/1729_header
[branch "article_v2/1630_edit_general_information"]
	remote = origin
	merge = refs/heads/article_v2/1630_edit_general_information
[branch "partial_inventory/release_product_inventory"]
	remote = origin
	merge = refs/heads/partial_inventory/release_product_inventory
[branch "article_v2/release_pondere"]
	remote = origin
	merge = refs/heads/article_v2/release_pondere
[branch "article_v2/compound/post_components_1726"]
	remote = origin
	merge = refs/heads/article_v2/compound/post_components_1726
[branch "master"]
	remote = origin
	merge = refs/heads/master
[branch "article_v2/1809_buyers_tab"]
	remote = origin
	merge = refs/heads/article_v2/1809_buyers_tab
[branch "article_v2/compound/release"]
	remote = origin
	merge = refs/heads/article_v2/compound/release
[branch "article_v2/logistic_info_1376"]
	remote = origin
	merge = refs/heads/article_v2/logistic_info_1376
[branch "article_v2/1779_supplier_order_display"]
	remote = origin
	merge = refs/heads/article_v2/1779_supplier_order_display
[branch "article_v2/1779_release"]
	remote = origin
	merge = refs/heads/article_v2/1779_release
[branch "article_v2/compound/1842_fix_dispo"]
	remote = origin
	merge = refs/heads/article_v2/compound/1842_fix_dispo
[branch "article_v2/compound/1842_release"]
	remote = origin
	merge = refs/heads/article_v2/compound/1842_release
[branch "article_v2/1442_release"]
	remote = origin
	merge = refs/heads/article_v2/1442_release
[branch "article_v2/1442_subtabs"]
	remote = origin
	merge = refs/heads/article_v2/1442_subtabs
[branch "article_v2/1442_subtabs_2"]
	remote = origin
	merge = refs/heads/article_v2/1442_subtabs_2
[branch "transfer/release"]
	remote = origin
	merge = refs/heads/transfer/release
[branch "transfer/1971_stalled_transfers"]
	remote = origin
	merge = refs/heads/transfer/1971_stalled_transfers
[branch "sales_channel/release"]
	remote = origin
	merge = refs/heads/sales_channel/release
[branch "sales_channel/1883"]
	remote = origin
	merge = refs/heads/sales_channel/1883
[branch "transfer/1972_ongoing_transfers"]
	remote = origin
	merge = refs/heads/transfer/1972_ongoing_transfers
[branch "transfer/1973_dipatch_notes"]
	remote = origin
	merge = refs/heads/transfer/1973_dipatch_notes
[branch "transfer/1953_stickers"]
	remote = origin
	merge = refs/heads/transfer/1953_stickers
[branch "transfer/1753_reprint_sticker"]
	remote = origin
	merge = refs/heads/transfer/1753_reprint_sticker
[branch "transfer/2055_dispatch_note"]
	remote = origin
	merge = refs/heads/transfer/2055_dispatch_note
[branch "promo_budget/2079"]
	remote = origin
	merge = refs/heads/promo_budget/2079
[branch "promo_budget/release"]
	remote = origin
	merge = refs/heads/promo_budget/release
[branch "promo_budget/2118_pa_net"]
	remote = origin
	merge = refs/heads/promo_budget/2118_pa_net
[branch "promo_budget/fix_null_amount"]
	remote = origin
	merge = refs/heads/promo_budget/fix_null_amount
[branch "pricing_strategy/release"]
	remote = origin
	merge = refs/heads/pricing_strategy/release
[branch "pricing_strategy/2217_low_margin"]
	remote = origin
	merge = refs/heads/pricing_strategy/2217_low_margin
[branch "pricing_strategy/2218_refacto_engine"]
	remote = origin
	merge = refs/heads/pricing_strategy/2218_refacto_engine
[branch "pricing_strategy/2218_renaming"]
	remote = origin
	merge = refs/heads/pricing_strategy/2218_renaming
[branch "multi-channel-pricing/v1/release"]
	remote = origin
	merge = refs/heads/multi-channel-pricing/v1/release
[branch "pricing_strategy/2218_preview"]
	remote = origin
	merge = refs/heads/pricing_strategy/2218_preview
[branch "chatbase"]
	remote = origin
	merge = refs/heads/chatbase
[branch "pricing_strategy/fix_competitors"]
	remote = origin
	merge = refs/heads/pricing_strategy/fix_competitors
[branch "support_2322"]
	remote = origin
	merge = refs/heads/support_2322
[branch "pricing_strategy/conflits"]
	remote = origin
	merge = refs/heads/pricing_strategy/conflits
[branch "pricing_strategy/2351"]
	remote = origin
	merge = refs/heads/pricing_strategy/2351
[branch "pricing_strategy/2355"]
	remote = origin
	merge = refs/heads/pricing_strategy/2355
[branch "pricing_strategy/fix_article_search_display"]
	remote = origin
	merge = refs/heads/pricing_strategy/fix_article_search_display
[branch "pricing_strategy/2367_nice_to_have"]
	remote = origin
	merge = refs/heads/pricing_strategy/2367_nice_to_have
[branch "pricing_strategy/release_2"]
	remote = origin
	merge = refs/heads/pricing_strategy/release_2
[branch "pricing_strategy/fix_price_edit"]
	remote = origin
	merge = refs/heads/pricing_strategy/fix_price_edit
[branch "pricing_strategy/perf"]
	remote = origin
	merge = refs/heads/pricing_strategy/perf
[branch "article_v2/subtabs_localstorage"]
	remote = origin
	merge = refs/heads/article_v2/subtabs_localstorage
[branch "2370_customer_order_api"]
	remote = origin
	merge = refs/heads/2370_customer_order_api
[branch "2370_release"]
	remote = origin
	merge = refs/heads/2370_release
[branch "support_2487"]
	remote = origin
	merge = refs/heads/support_2487
[branch "support_2487_2"]
	remote = origin
	merge = refs/heads/support_2487_2
[branch "validation"]
	remote = origin
	merge = refs/heads/validation
[branch "support_2305"]
	remote = origin
	merge = refs/heads/support_2305
[branch "customer_order_margin"]
	remote = origin
	merge = refs/heads/customer_order_margin
