<script setup lang="ts">
import ErpButton from '@/shared/components/buttons/ErpButton.vue'
import ErpFormBlock from '@/shared/components/form/ErpFormBlock.vue'
import ErpInputHelper from '@/shared/components/form/ErpInputHelper.vue'
import { useArticlePage } from '@/apps/erp/composable/useArticlePage'
import ErpSkeleton from '@/shared/components/ui/ErpSkeleton.vue'
import { useAuthenticationStore } from '@/stores/authentication'
import { computed, nextTick, ref, watch } from 'vue'
import ErpInputWithAddOn from '@/shared/components/form/ErpInputWithAddOn.vue'
import { formatCurrency, formatNumber, formatDate } from '@/shared/string_utils'
import ErpTooltip from '@/shared/components/ui/ErpTooltip.vue'
import { ARTICLE_DESTOCK_WRITE, ARTICLE_PRICES_WRITE } from '@/apps/erp/permissions'
import ErpMultiselect from '@/shared/components/form/ErpMultiselect.vue'
import Badge from '@/shared/components/ui/Badge.vue'
import labels from '@/apps/erp/components/Article/articleLabels'
import WeightedCostAdjustments from '@/apps/erp/components/Article/Buyers/WeightedCostAdjustments.vue'
import { PRICES_MESSAGES } from '@/apps/erp/components/Article/articleValidation'
import { useArticlePrice } from '@/apps/erp/composable/useArticlePrice'
import { useArticleMarginCalculator } from '@/apps/erp/composable/useArticleMarginCalculator'
import SlideOutContainer from '@/shared/components/ui/SlideOutContainer.vue'
import PriceInput from '@/shared/components/form/PriceInput.vue'
import { useUnconditionalDiscount } from '@/apps/erp/composable/useUnconditionalDiscount'
import { round } from 'lodash'
import type { SellingPriceMarginComputation } from '@/shared/api/erp_server/sales_channel/types'
import { PutArticleScope } from '@/shared/api/erp_server/article/putArticle'
import { useToastStore } from '@/services/plugin/toast/stores'

const { prices } = useArticlePrice()

interface Props {
    packagedArticlesTotalSellingPrice?: number | null
}

const props = withDefaults(defineProps<Props>(), {
    packagedArticlesTotalSellingPrice: () => null,
})

const { article, updateArticle, validation_messages, is_updating_article, svd_sales_channel } = useArticlePage()
const { edited_unconditional_discount, unconditional_discount, submitUnconditionalDiscount } =
    useUnconditionalDiscount()
const {
    updateSalesChannelComputation,
    throttledHandleSellingPriceMarginComputationCheck,
    buildContext,
    svd_computed_sales_channel,
    svd_computed_sales_channel_margin_error,
} = useArticleMarginCalculator()

/*
for regular articles :
  2-col. section for purchase prices
  2-col. section for selling prices
  1-col. section for ecotax and sorecop
  1-col. section for reseller and 3% price

for package articles :
  1-col. section for purchase prices
  3-col. section for selling prices
  1-col. section for ecotax and sorecop

responsiveness:
  above xl : all sections are displayed in one line, each containing 2 rows of form-blocks
  above lg : the line breaks before the last section
  above md: the line breaks after 2 sections, in which form blocks are displayed as a single column
  below md: the line breaks after each section, affectively displaying one column of form-blocks
  below xxs: Heisenberg's uncertainty principle prevents us to determine both the position and speed of form-blocks
 */
const container_class = computed(() => {
    return [
        'grid grid-cols-1 md:grid-cols-2 p-2 gap-3 w-full',
        article.value.is_package ? 'lg:grid-cols-3 xl:grid-cols-5' : 'lg:grid-cols-3 2xl:grid-cols-6',
    ]
})

const section_classes = 'flex flex-col p-2 gap-3 border border-dashed border-slate-300 rounded bg-white'

const selling_prices_section_class = computed(() => [article.value?.is_package ? 'xl:col-span-3' : '2xl:col-span-2'])

const purchase_prices_section_class = computed(() => [
    section_classes,
    article.value?.is_package ? '' : '2xl:col-span-2',
])

const input_shared_props = {
    type: 'number',
    inputClasses: 'text-right',
    trailingClasses: 'text-slate-400',
}

const has_prices_permission = computed(() => useAuthenticationStore().hasPermission(ARTICLE_PRICES_WRITE))
const has_destock_prices_permission = computed(() => useAuthenticationStore().hasPermission(ARTICLE_DESTOCK_WRITE))

const is_disabled = computed(
    () => is_updating_article.value || (!has_prices_permission.value && !article.value?.is_destock),
)

const is_disabled_destock = computed(
    () => is_updating_article.value || (!has_prices_permission.value && !has_destock_prices_permission.value),
)

const display_server_margin = ref(true)
const selling_price = computed(() => prices.value?.selling_price)
const tariff_tax_excluded = computed(() => prices.value?.tariff_tax_excluded)
const ecotax = computed(() => prices.value?.ecotax)
const sorecop = computed(() => prices.value?.sorecop)
const weighted_cost = computed(() => prices.value?.weighted_cost)
const PRICES_USED_FOR_MARGIN = [selling_price, ecotax, sorecop, weighted_cost, tariff_tax_excluded]
const price_watchers_enabled = ref(true)
const is_weighted_costs_adjustments_opened = ref(false)

watch(PRICES_USED_FOR_MARGIN, () => {
    if (price_watchers_enabled.value) {
        display_server_margin.value = false
    }
})

const onPriceMarginComputationSuccess = () => {
    display_server_selling_price_tax_excluded.value = true
    display_server_margin.value = true
}

const onPriceMarginComputationError = () => {
    useToastStore().add({ type: 'danger', content: 'Une erreur est survenue dans le calcul de la marge.' })
}
const triggerUpdateMarginComputation = () => {
    display_server_selling_price_tax_excluded.value = false
    const context = buildContext(article.value, svd_sales_channel.value, prices.value.selling_price)
    throttledHandleSellingPriceMarginComputationCheck(
        context,
        onPriceMarginComputationSuccess,
        onPriceMarginComputationError,
    )
}

const display_server_selling_price_tax_excluded = ref(true)
watch(selling_price, () => {
    if (price_watchers_enabled.value) {
        display_server_selling_price_tax_excluded.value = false
        triggerUpdateMarginComputation()
    }
})

const display_server_tariff_tax_included = ref(true)
watch(tariff_tax_excluded, () => {
    if (price_watchers_enabled.value) {
        display_server_tariff_tax_included.value = false
        triggerUpdateMarginComputation()
    }
})

const margin = computed(() => {
    if (display_server_margin.value) {
        return round(svd_computed_sales_channel.value?.margin, 2)
    }
    return 0
})

const margin_rate = computed(() => {
    if (display_server_margin.value) {
        return `Taux de marque: ${formatRate(svd_computed_sales_channel.value?.margin_rate)}`
    }
    return 'Calcul de la marge...'
})

const formatRate = (rate: number) => `${formatNumber(rate * 100)} %`

const tariff_tax_included = computed(() => {
    if (!prices.value) {
        return 0
    }

    return display_server_tariff_tax_included.value
        ? prices.value.tariff_tax_included
        : prices.value.tariff_tax_excluded * (1 + prices.value.vat)
})

const packaged_articles_total_selling_price_tax_excluded = computed(() => {
    if (!prices.value) {
        return 0
    }

    return props.packagedArticlesTotalSellingPrice !== null
        ? props.packagedArticlesTotalSellingPrice / (1 + prices.value.vat)
        : null
})

const package_discount_tax_included = computed(() => {
    if (!prices.value) {
        return 0
    }

    return props.packagedArticlesTotalSellingPrice !== null
        ? prices.value.selling_price - props.packagedArticlesTotalSellingPrice
        : null
})

const package_discount_rate = computed(() =>
    package_discount_tax_included.value &&
    props.packagedArticlesTotalSellingPrice &&
    props.packagedArticlesTotalSellingPrice > 0
        ? formatRate(package_discount_tax_included.value / props.packagedArticlesTotalSellingPrice)
        : '--',
)

const ecotax_tax_excluded = computed(() => {
    if (!prices.value) {
        return 0
    }

    return prices.value.ecotax / (1 + prices.value.vat)
})
const sorecop_tax_excluded = computed(() => {
    if (!prices.value) {
        return 0
    }

    return prices.value.sorecop / (1 + prices.value.vat)
})

function savePrices() {
    if (JSON.stringify(prices.value) === JSON.stringify(article.value.prices)) {
        return
    }

    updateArticle(
        article.value.is_destock ? PutArticleScope.GENERAL_DESTOCK_PRICES : PutArticleScope.PRICES,
        prices.value,
    )
}

const onSubmit = () => {
    if (!prices.value) {
        return
    }

    if (edited_unconditional_discount.value.amount !== unconditional_discount.value.amount) {
        submitUnconditionalDiscount(
            edited_unconditional_discount.value,
            () => {
                useToastStore().add({
                    type: 'danger',
                    content: 'Une erreur est survenue dans la sauvegarde de la remise inconditionnelle.',
                })
            },
            () => {
                savePrices()
            },
        )
    } else {
        savePrices()
    }
}

watch(article, () => {
    price_watchers_enabled.value = false
    if (article.value?.prices) {
        prices.value = structuredClone(article.value.prices)
    }
    for (const computation of article.value.sales_channels as SellingPriceMarginComputation[]) {
        updateSalesChannelComputation(computation)
    }
    display_server_margin.value = true
    display_server_selling_price_tax_excluded.value = true
    display_server_tariff_tax_included.value = true
    nextTick(() => {
        price_watchers_enabled.value = true
    })
})

watch(
    edited_unconditional_discount,
    (newValue, oldValue) => {
        if (oldValue.amount !== null) {
            triggerUpdateMarginComputation()
        }
    },
    { deep: true },
)

watch(
    prices,
    (newValue, oldValue) => {
        if (JSON.stringify(newValue) !== JSON.stringify(oldValue)) {
            triggerUpdateMarginComputation()
        }
    },
    { deep: true },
)

interface InitialSellingPriceOption {
    key: string
    label: string
    value: number
    date: string | null
    declination_id: number | null
}

const initial_selling_price_options = computed<InitialSellingPriceOption[]>(() => {
    const options = (prices.value?.selling_price_history ?? [])
        .map((e) => {
            return {
                key: `${e.selling_price}-${e.declination_id}`,
                label: formatNumber(e.selling_price),
                value: e.selling_price,
                date: e.started_at,
                declination_id: e.declination_id,
            }
        })
        .sort((a, b) => Date.parse(a.date) - Date.parse(b.date))
    return [
        {
            key: '0-0',
            label: 'Aucun',
            value: 0,
            date: null,
            declination_id: null,
        },
        ...options,
    ]
})

function onSelectInitialSellingPrice(option: InitialSellingPriceOption) {
    if (!prices.value) {
        return
    }

    prices.value.initial_selling_price = option.value
}

const selling_price_taxes_excluded = computed(() => {
    if (svd_computed_sales_channel.value !== undefined) {
        return formatCurrency(svd_computed_sales_channel.value?.selling_price_tax_excluded ?? 0) + ' HT'
    }

    if (prices.value) {
        return formatCurrency(prices.value.selling_price_tax_excluded) + ' HT'
    }
})

const selling_price_validation_message = computed(() => {
    if (validation_messages.value.selling_price) {
        return validation_messages.value.selling_price
    }

    if (
        article.value.is_package &&
        props.packagedArticlesTotalSellingPrice !== null &&
        prices.value &&
        prices.value.selling_price > props.packagedArticlesTotalSellingPrice
    ) {
        return PRICES_MESSAGES.package_selling_price_too_high
    }

    return svd_computed_sales_channel_margin_error.value
})

const package_discount_validation_message = computed(() => {
    if (
        article.value.is_package &&
        package_discount_tax_included.value !== null &&
        package_discount_tax_included.value > 0
    ) {
        return PRICES_MESSAGES.positive_package_discount
    }

    return undefined
})
</script>

<template>
    <div data-context="article-v2-buyers-prices">
        <erp-skeleton v-if="!prices" page />
        <form v-else class="flex flex-col divide-y divide-slate-200 border border-slate-200 rounded">
            <div :class="container_class">
                <section :class="purchase_prices_section_class">
                    <div
                        class="grid grid-cols-1 gap-3"
                        :class="{
                            '2xl:grid-cols-2': !article.is_package,
                            'lg:grid-cols-1': article.is_package,
                        }"
                    >
                        <!-- sub-section -->
                        <div>
                            <erp-form-block
                                :label="labels.tariff_tax_excluded"
                                classes=""
                                data-context="tariff-tax-excluded"
                            >
                                <erp-tooltip
                                    :message="{
                                        disabled: !article.is_package,
                                        content: 'Non éditable pour un composé',
                                        placement: 'top-end',
                                        arrowPadding: 10,
                                    }"
                                >
                                    <price-input
                                        v-model="prices.tariff_tax_excluded"
                                        data-context="tariff-tax-excluded-input"
                                        suffix="€ HT"
                                        :subtitle="formatCurrency(tariff_tax_included) + ' TTC'"
                                        :validation-message="validation_messages.tariff_tax_excluded"
                                        :disabled="
                                            article.is_package ||
                                            (article.is_destock ? is_disabled_destock : is_disabled)
                                        "
                                    />
                                </erp-tooltip>
                            </erp-form-block>

                            <erp-form-block
                                v-if="!article.is_package"
                                :label="labels.purchase_tax_excluded"
                                classes=""
                                data-context="purchase-tax-excluded"
                            >
                                <erp-input-with-add-on
                                    v-bind="input_shared_props"
                                    v-model="prices.purchase_tax_excluded"
                                    disabled
                                    :is-invalid="validation_messages.purchase_tax_excluded !== undefined"
                                >
                                    <template #trailing>€ HT</template>
                                </erp-input-with-add-on>
                                <erp-input-helper
                                    v-if="validation_messages.purchase_tax_excluded !== undefined"
                                    is-invalid
                                    class="text-right pr-3"
                                    >{{ validation_messages.purchase_tax_excluded }}
                                </erp-input-helper>
                            </erp-form-block>
                        </div>

                        <!-- sub-section -->
                        <div>
                            <erp-form-block label="Prix pondéré" classes="" data-context="weighted_cost">
                                <div class="w-full flex items-center gap-1">
                                    <erp-tooltip
                                        :message="{
                                            content: 'Non éditable',
                                            placement: 'bottom-start',
                                            arrowPadding: 10,
                                        }"
                                        class="w-full"
                                    >
                                        <erp-input-with-add-on
                                            data-context="weighted-cost-input"
                                            disabled
                                            v-bind="input_shared_props"
                                            :value="prices.weighted_cost"
                                        >
                                            <template #trailing>€ HT</template>
                                        </erp-input-with-add-on>
                                    </erp-tooltip>
                                    <erp-tooltip v-if="!article.is_package" message="Ajustements">
                                        <erp-button
                                            tertiary
                                            data-context="weighted-costs-adjustments-button"
                                            :icon="['fas', has_prices_permission ? 'pencil' : 'eye']"
                                            icon-class="text-blue-500"
                                            @click.prevent="is_weighted_costs_adjustments_opened = true"
                                        />
                                    </erp-tooltip>
                                </div>
                            </erp-form-block>
                        </div>
                    </div>
                </section>

                <section :class="selling_prices_section_class">
                    <div
                        class="grid grid-cols-1 gap-3"
                        :class="{
                            '2xl:grid-cols-2': !article.is_package,
                            'xl:grid-cols-3': article.is_package,
                        }"
                    >
                        <!-- sub-section - highlighted because Cyril Renaud ABSOLUTELY NEEDS IT -->
                        <div class="flex flex-col p-2 gap-3 border-[2.5px] border-dashed border-yellow-500 rounded">
                            <erp-form-block :label="labels.selling_price" classes="" data-context="selling-price">
                                <price-input
                                    v-model="prices.selling_price"
                                    data-context="selling-price-input"
                                    :subtitle="selling_price_taxes_excluded"
                                    subtitle-line2="L’édition de prix des autres canaux est possible depuis l’onglet canaux de vente"
                                    :validation-message="selling_price_validation_message"
                                    disabled
                                />
                            </erp-form-block>

                            <erp-form-block label="Marge" classes="" data-context="margin">
                                <erp-tooltip
                                    :message="{ content: 'Non éditable', placement: 'top-end', arrowPadding: 10 }"
                                >
                                    <price-input
                                        data-context="margin-input"
                                        :value="margin"
                                        :subtitle="margin_rate"
                                        suffix="€ HT"
                                        disabled
                                    />
                                </erp-tooltip>
                            </erp-form-block>
                        </div>

                        <!-- sub-section -->
                        <div :class="section_classes">
                            <erp-form-block :label="labels.pvgc" classes="" data-context="pvgc">
                                <erp-tooltip
                                    :message="{
                                        disabled: !article.is_package,
                                        content: 'Non éditable pour un composé',
                                        placement: 'top-end',
                                        arrowPadding: 10,
                                    }"
                                >
                                    <erp-input-with-add-on
                                        v-bind="input_shared_props"
                                        v-model="prices.pvgc"
                                        :is-invalid="validation_messages.pvgc !== undefined"
                                        :disabled="
                                            article.is_package ||
                                            (article.is_destock ? is_disabled_destock : is_disabled)
                                        "
                                    >
                                        <template #trailing>€ TTC</template>
                                    </erp-input-with-add-on>
                                </erp-tooltip>
                                <erp-input-helper
                                    v-if="validation_messages.pvgc !== undefined"
                                    is-invalid
                                    class="text-right pr-3"
                                    >{{ validation_messages.pvgc }}
                                </erp-input-helper>
                            </erp-form-block>

                            <erp-form-block
                                v-if="!article.is_package"
                                :label="labels.initial_selling_price"
                                classes=""
                                data-context="initial-selling-price"
                            >
                                <erp-multiselect
                                    :options="initial_selling_price_options"
                                    :value="[`${prices.initial_selling_price}`]"
                                    :is-invalid="validation_messages.initial_selling_price !== undefined"
                                    :disabled="
                                        article.is_package || (article.is_destock ? is_disabled_destock : is_disabled)
                                    "
                                    track-by="key"
                                    hide-internal-search
                                    close-on-select
                                    @select="onSelectInitialSellingPrice"
                                >
                                    <template #single="{ selected_value }">
                                        <span class="flex justify-end pr-4 items-center gap-x-1">
                                            <template v-if="selected_value.label === '0'">Aucun</template>
                                            <template v-else
                                                >{{ formatNumber(selected_value.label)
                                                }}<span class="text-slate-400"> € TTC</span>
                                            </template>
                                        </span>
                                    </template>
                                    <template #suggestion="{ option }">
                                        <span class="ml-auto grid grid-cols-2 justify-items-end items-center gap-x-2">
                                            <span class="col-span-2 whitespace-nowrap"
                                                >{{ option.label }}
                                                <span v-if="option._original.value" class="text-slate-400">
                                                    € TTC</span
                                                ></span
                                            >
                                            <badge v-if="option._original.declination_id" color="gray"
                                                >déclinaison</badge
                                            >
                                            <span
                                                v-if="option._original.date"
                                                :class="{ 'col-span-2': !option._original.declination_id }"
                                                class="whitespace-nowrap justify-items-end text-slate-400"
                                                >{{ formatDate(option._original.date, {}) }}</span
                                            >
                                        </span>
                                    </template>
                                </erp-multiselect>
                                <erp-input-helper
                                    v-if="validation_messages.initial_selling_price !== undefined"
                                    is-invalid
                                    class="text-right pr-3"
                                    >{{ validation_messages.initial_selling_price }}
                                </erp-input-helper>
                            </erp-form-block>

                            <erp-form-block
                                v-if="article.is_package"
                                label="Prix vente articles"
                                classes=""
                                data-context="package-articles-total-selling-price"
                            >
                                <erp-tooltip
                                    :message="{ content: 'Non éditable', placement: 'top-end', arrowPadding: 10 }"
                                >
                                    <price-input
                                        v-model="props.packagedArticlesTotalSellingPrice"
                                        data-context="package-articles-total-selling-price-input"
                                        :subtitle="
                                            formatCurrency(packaged_articles_total_selling_price_tax_excluded) + ' HT'
                                        "
                                        :validation-message="package_discount_validation_message"
                                        :disabled="true"
                                    />
                                </erp-tooltip>
                            </erp-form-block>
                        </div>

                        <!-- sub-section -->
                        <div v-if="article.is_package" :class="section_classes">
                            <erp-form-block label="Remise" classes="" data-context="package-discount">
                                <erp-tooltip
                                    :message="{ content: 'Non éditable', placement: 'top-end', arrowPadding: 10 }"
                                >
                                    <price-input
                                        data-context="package-discount-input"
                                        :v-bind="input_shared_props"
                                        :value="package_discount_tax_included"
                                        :validation-message="package_discount_validation_message"
                                        :subtitle="package_discount_rate"
                                        :disabled="true"
                                    />
                                </erp-tooltip>
                            </erp-form-block>
                        </div>
                    </div>
                </section>

                <div class="grid grid-cols-1 gap-3" :class="{ '2xl:col-span-2 2xl:grid-cols-2': !article.is_package }">
                    <section :class="section_classes">
                        <erp-form-block :label="labels.ecotax" classes="" data-context="ecotax">
                            <erp-tooltip
                                :message="{
                                    disabled: !article.is_package,
                                    content: 'Non éditable pour un composé',
                                    placement: 'top-end',
                                    arrowPadding: 10,
                                }"
                            >
                                <price-input
                                    v-model="prices.ecotax"
                                    data-context="ecotax-input"
                                    :disabled="
                                        article.is_package || (article.is_destock ? is_disabled_destock : is_disabled)
                                    "
                                    :subtitle="formatCurrency(ecotax_tax_excluded) + ' HT'"
                                    :validation-message="validation_messages.ecotax"
                                />
                            </erp-tooltip>
                        </erp-form-block>
                        <erp-form-block :label="labels.sorecop" classes="" data-context="sorecop">
                            <erp-tooltip
                                :message="{
                                    disabled: !article.is_package,
                                    content: 'Non éditable pour un composé',
                                    placement: 'top-end',
                                    arrowPadding: 10,
                                }"
                            >
                                <price-input
                                    v-model="prices.sorecop"
                                    data-context="sorecop-input"
                                    :disabled="
                                        article.is_package || (article.is_destock ? is_disabled_destock : is_disabled)
                                    "
                                    :subtitle="formatCurrency(sorecop_tax_excluded) + ' HT'"
                                    :validation-message="validation_messages.sorecop"
                                />
                            </erp-tooltip>
                            <erp-input-helper
                                v-if="validation_messages.sorecop !== undefined"
                                is-invalid
                                class="text-right pr-3"
                                >{{ validation_messages.sorecop }}
                            </erp-input-helper>
                        </erp-form-block>
                    </section>

                    <section v-if="!article.is_package" :class="section_classes">
                        <erp-form-block :label="labels.reseller_price" classes="" data-context="reseller-price">
                            <erp-input-with-add-on
                                v-bind="input_shared_props"
                                v-model="prices.reseller_price"
                                :is-invalid="validation_messages.reseller_price !== undefined"
                                :disabled="
                                    article.is_package || (article.is_destock ? is_disabled_destock : is_disabled)
                                "
                            >
                                <template #trailing>€ HT</template>
                            </erp-input-with-add-on>
                            <erp-input-helper
                                v-if="validation_messages.reseller_price !== undefined"
                                is-invalid
                                class="text-right pr-3"
                                >{{ validation_messages.reseller_price }}
                            </erp-input-helper>
                        </erp-form-block>

                        <erp-form-block label="Prix 3%" classes="" data-context="intragroup">
                            <erp-tooltip
                                :message="{ content: 'Non éditable', placement: 'bottom-start', arrowPadding: 10 }"
                            >
                                <erp-input-with-add-on disabled v-bind="input_shared_props" :value="prices.intragroup">
                                    <template #trailing>€ TTC</template>
                                </erp-input-with-add-on>
                            </erp-tooltip>
                        </erp-form-block>
                    </section>
                </div>
            </div>

            <div class="p-3 bg-slate-50">
                <erp-tooltip :denied="article.is_destock ? is_disabled_destock : is_disabled">
                    <erp-button
                        type="submit"
                        :disabled="article.is_destock ? is_disabled_destock : is_disabled"
                        data-context="submit-button"
                        @click.prevent="onSubmit"
                        >Sauvegarder
                    </erp-button>
                </erp-tooltip>
            </div>
        </form>
        <slide-out-container
            :is-open="is_weighted_costs_adjustments_opened"
            @close="is_weighted_costs_adjustments_opened = false"
        >
            <weighted-cost-adjustments :article="article" />
        </slide-out-container>
    </div>
</template>
