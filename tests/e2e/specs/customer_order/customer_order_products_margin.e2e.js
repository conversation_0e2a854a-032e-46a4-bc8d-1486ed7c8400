import { NON_BREAKING_SPACE } from '../../utils/text-utils'

describe('Customer order products margin', () => {
    const VISIT_PAGE = (file) => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {},
            },
        }).as('customer_order')

        cy.intercept('POST', '**/api/erp/v1/customer-order-products', { fixture: file }).as(
            'cpost_customer_order_products',
        )

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.emitEvent('open-products-margin', 123456)
        cy.wait('@customer_order')
        cy.wait('@cpost_customer_order_products')
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Show products margin in side panel', () => {
        it('displays all products margin for a customer order', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .should('exist')

            cy.get('@panel').find('[data-context=page-header]').should('contain', 'Marges produits')

            cy.closeAllToasts()

            // Check table header
            cy.get('@panel').find('[data-context=erp-table]').as('table')
            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 6)
            cy.get('@header').eq(0).should('contain', 'Produit')
            cy.get('@header').eq(1).should('contain', 'Qté')
            cy.get('@header').eq(2).should('contain', 'Prix de vente TTC')
            cy.get('@header').eq(3).should('contain', 'Remise TTC')
            cy.get('@header').eq(4).should('contain', 'Marge TTC')

            // Check products rows (3 products + 1 total row = 4 rows)
            cy.get('@panel').find('[data-context=table-row]').should('have.length', 4)

            // First product: S809 HCS Bois noir
            cy.get('@panel').find('[data-context=table-row]').eq(0).as('product1')
            cy.get('@product1')
                .find('[data-context=erp-article-item] [data-context=sku]')
                .should('contain', 'JAMOS809HCSBNR')
            cy.get('@product1')
                .find('[data-context=erp-article-item] [data-context=name]')
                .should('contain', 'S809 HCS Bois noir')
            cy.get('@product1').should('contain', '2') // quantity
            cy.get('@product1').should('contain', '599,98' + NON_BREAKING_SPACE + '€') // selling price: 299.99 * 2
            cy.get('@product1').should('contain', '15,50' + NON_BREAKING_SPACE + '€') // discount
            cy.get('@product1').should('contain', '190,50' + NON_BREAKING_SPACE + '€') // margin: 95.25 * 2

            // Second product: S810 SUB Bois noir
            cy.get('@panel').find('[data-context=table-row]').eq(1).as('product2')
            cy.get('@product2')
                .find('[data-context=erp-article-item] [data-context=sku]')
                .should('contain', 'JAMOS810SUBNR')
            cy.get('@product2')
                .find('[data-context=erp-article-item] [data-context=name]')
                .should('contain', 'S810 SUB Bois noir')
            cy.get('@product2').should('contain', '1') // quantity
            cy.get('@product2').should('contain', '199,99' + NON_BREAKING_SPACE + '€') // selling price: 199.99 * 1
            cy.get('@product2').should('contain', '8,00' + NON_BREAKING_SPACE + '€') // discount
            cy.get('@product2').should('contain', '65,50' + NON_BREAKING_SPACE + '€') // margin: 65.50 * 1

            // Third product: Câble HDMI 2m
            cy.get('@panel').find('[data-context=table-row]').eq(2).as('product3')
            cy.get('@product3')
                .find('[data-context=erp-article-item] [data-context=sku]')
                .should('contain', 'CABHDMI2M')
            cy.get('@product3')
                .find('[data-context=erp-article-item] [data-context=name]')
                .should('contain', 'Câble HDMI 2m')
            cy.get('@product3').should('contain', '3') // quantity
            cy.get('@product3').should('contain', '38,97' + NON_BREAKING_SPACE + '€') // selling price: 12.99 * 3
            cy.get('@product3').should('contain', '2,50' + NON_BREAKING_SPACE + '€') // discount
            cy.get('@product3').should('contain', '23,55' + NON_BREAKING_SPACE + '€') // margin: 7.85 * 3

            // Total row
            cy.get('@panel').find('[data-context=table-row]').eq(3).as('total')
            cy.get('@total').should('contain', 'TOTAL')
            cy.get('@total').find('.font-bold.text-gray-900').should('exist')
            cy.get('@total').should('contain', '6') // total quantity: 2 + 1 + 3
            cy.get('@total').should('contain', '838,94' + NON_BREAKING_SPACE + '€') // total selling price: 599.98 + 199.99 + 38.97
            cy.get('@total').should('contain', '26,00' + NON_BREAKING_SPACE + '€') // total discount: 15.50 + 8.00 + 2.50
            cy.get('@total').should('contain', '279,55' + NON_BREAKING_SPACE + '€') // total margin: 190.50 + 65.50 + 23.55
        })

        it('displays empty state when no products', () => {
            cy.intercept('POST', '**/api/erp/v1/customer-order-products', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        customer_order_products: [],
                    },
                },
            }).as('cpost_customer_order_products_empty')

            cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')
            cy.emitEvent('open-products-margin', 123456)
            cy.wait('@cpost_customer_order_products_empty')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .should('exist')

            cy.get('@panel').find('[data-context=page-header]').should('contain', 'Marges produits')
            cy.get('@panel').find('[data-context=table-row]').should('not.exist')
        })

        it('handles API error gracefully', () => {
            cy.intercept('POST', '**/api/erp/v1/customer-order-products', {
                statusCode: 500,
                body: { error: 'Internal server error' },
            }).as('cpost_customer_order_products_error')

            cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')
            cy.emitEvent('open-products-margin', 123456)
            cy.wait('@cpost_customer_order_products_error')

            // Should display error toast
            cy.get('[data-context=toast-container]').should('exist')
            cy.get('[data-context=toast]').should('contain', 'Une erreur est survenue lors de la récupération des données')
        })

        it('displays loading state initially', () => {
            // Intercept with delay to see loading state
            cy.intercept('POST', '**/api/erp/v1/customer-order-products', (req) => {
                req.reply((res) => {
                    res.delay(1000)
                    res.send({ fixture: 'erp/customer-order-products/customer_order_products_margin.json' })
                })
            }).as('cpost_customer_order_products_delayed')

            cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')
            cy.emitEvent('open-products-margin', 123456)

            // During loading, the products-availability div should not exist
            cy.get('[data-context=slide-out-container]').should('exist')
            cy.get('[data-context=products-availability]').should('not.exist')

            cy.wait('@cpost_customer_order_products_delayed')

            // After loading, content should appear
            cy.get('[data-context=products-availability]').should('exist')
        })

        it('formats currency values correctly', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]').as('panel')

            // Check that all currency values are properly formatted with € symbol and French formatting
            cy.get('@panel').should('contain', '599,98' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '199,99' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '38,97' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '838,94' + NON_BREAKING_SPACE + '€')
        })

        it('displays article images and links correctly', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]').as('panel')

            // Check first product article item
            cy.get('@panel').find('[data-context=table-row]').eq(0).within(() => {
                cy.get('[data-context=erp-article-item]').should('exist')
                cy.get('[data-context=image]')
                    .should('have.attr', 'src')
                    .should('include', '/images/article/jamo/JAMOS809HCSBNR/s809-hcs-bois-noir_5a5f346ee9538_300_square.jpg')
                cy.get('[data-context=sku]')
                    .should('have.attr', 'href', '/articles/JAMOS809HCSBNR/')
            })
        })
    })
})
