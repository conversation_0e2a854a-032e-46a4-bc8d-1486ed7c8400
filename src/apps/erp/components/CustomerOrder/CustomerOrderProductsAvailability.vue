<template>
    <div v-if="!is_loading" data-context="products-availability">
        <page-header>
            <span class="mr-3">Disponibilité commande</span>

            <badge :color="customer_order_availability_status.color">
                {{ customer_order_availability_status.label }}
            </badge>
        </page-header>

        <erp-table :columns="columns" :rows="mapped_products">
            <template #article="{ row }">
                <erp-article-item show-actions :sku="row.sku" :name="row.article_name" :image="row.article_image" />
            </template>

            <template #quantity="{ row }"> {{ row.availability[0].customer_order_product_quantity }} </template>

            <template #availability="{ row }">
                <badge :color="getProductAvailability(row).color"> {{ getProductAvailability(row).label }} </badge>
            </template>

            <template #expected_at="{ row }">
                <div v-if="row._availability_status === 'replenishment'" data-context="content">
                    <date-formatter :date="row.availability[0].availability_date" format="DD/MM/YYYY" />
                </div>

                <div
                    v-else-if="
                        row._availability_status === 'unknown' && (row.available_supplier_orders ?? []).length > 0
                    "
                    class="flex flex-col gap-1"
                    data-context="content"
                >
                    <scoped-badge
                        v-for="supplier_order of row.available_supplier_orders"
                        :key="supplier_order.supplier_order_id"
                        scope="cmd frn"
                    >
                        <erp-link
                            :to="`${erp_base_url}/legacy/commandeFournisseur/edition?id_commande_fournisseur=${supplier_order.supplier_order_id}`"
                            target="_blank"
                        >
                            {{ supplier_order.supplier_order_id }}
                        </erp-link>
                    </scoped-badge>
                </div>

                <div
                    v-else-if="
                        ['available', 'available_in_another_store', 'in_transfer'].includes(row._availability_status) &&
                        (row.available_transfers ?? []).length > 0
                    "
                    class="flex flex-col gap-1"
                    data-context="content"
                >
                    <scoped-badge v-for="transfer of row.available_transfers" :key="transfer.transfer_id" scope="trf">
                        <erp-link
                            :to="`${erp_base_url}/legacy/stock/bonTransfertEdit?id=${transfer.transfer_id}`"
                            target="_blank"
                        >
                            {{ transfer.transfer_id }}
                        </erp-link>
                    </scoped-badge>
                </div>

                <template v-else>&nbsp;</template>
            </template>

            <template #row_expander="{ row, column_names }">
                <tr v-if="showExpander" data-context="more-customer-orders">
                    <td :colspan="column_names.length">
                        <erp-link
                            :to="`${erp_base_url}/legacy/commande/recherche?filter=sku_attente&value=${row.sku}`"
                            target="_blank"
                            class="ml-2"
                        >
                            Voir autres commandes en attente
                        </erp-link>
                    </td>
                </tr>
            </template>
        </erp-table>
    </div>
</template>

<script>
import { cPostCustomerOrderProducts } from '@/shared/api/erp_server'
import Badge from '@/shared/components/ui/Badge.vue'
import ScopedBadge from '@/shared/components/ui/ScopedBadge.vue'
import { DateFormatter } from '@/shared/components'
import ErpLink from '@/shared/components/link/ErpLink.vue'
import { useToastStore } from '@/services/plugin/toast/stores'
import { ErpTable } from '@/shared/components/ErpTable'
import PageHeader from '@/shared/components/ui/PageHeader.vue'
import ErpArticleItem from '@/shared/components/article/ErpArticleItem.vue'
import { AVAILABILITY_STATUSES } from '@/apps/erp/components/CustomerOrder/constants'

const SORT_ORDER = [
    'unknown',
    'replenishment',
    'shipping_available_in_another_store',
    'shipping_bookable_from_another_store',
    'bookable_from_another_store',
    'available_in_another_store',
    'in_transfer',
    'bookable',
    'available',
    'available_in_store',
    'delivered',
    'delivered_from_store',
]

export default {
    name: 'CustomerOrderProductsAvailability',
    components: { ErpArticleItem, PageHeader, ErpTable, Badge, ScopedBadge, DateFormatter, ErpLink },
    props: {
        showExpander: {
            type: Boolean,
            default: true,
        },
        customerOrderId: {
            type: Number,
            required: true,
        },
    },
    data() {
        return {
            is_loading: false,
            products: null,
            columns: [
                {
                    name: 'article',
                    title: 'Produit',
                },
                {
                    name: 'quantity',
                    title: 'Qté',
                },
                {
                    name: 'availability',
                    title: 'Dispo',
                },
                {
                    name: 'expected_at',
                    title: 'Livraisons prévues',
                },
            ],
        }
    },
    computed: {
        erp_base_url() {
            return import.meta.env.VITE_APP_ERP_BASE_URL
        },
        params() {
            return {
                where: {
                    _and: [
                        {
                            customer_order_id: {
                                _eq: this.customerOrderId,
                            },
                        },
                    ],
                },
                included_dependencies: ['availability', 'available_supplier_orders', 'available_transfers'],
                limit: 9999,
            }
        },
        /**
         * Add its availability status to each product
         */
        mapped_products() {
            return this.products
                .map((product) => {
                    return Object.assign({}, product, {
                        _availability_status: this.getProductAvailabilityStatus(product),
                    })
                })
                .sort((a, b) => SORT_ORDER.indexOf(a._availability_status) - SORT_ORDER.indexOf(b._availability_status))
        },
        /**
         * 3 possible statuses on customer order :
         * Livrée : all products have been delivered or picked up in store
         * Livrable : all products are available for delivery or picking up in store (for valid customer order)
         * Réservable : all products are bookable for delivery or picking up in  store (for invalid customer order)
         * Non livrable : at least one product is not in stock (or not in store for pickup customer orders)
         */
        customer_order_availability_status() {
            const DELIVERED_STATUSES = ['delivered', 'delivered_from_store']
            const DELIVERABLE_STATUSES = [
                'available',
                'available_in_store',
                'available_in_another_store',
                'shipping_available_in_another_store',
                ...DELIVERED_STATUSES,
            ]
            const BOOKABLE_STATUSES = [
                'bookable',
                'shipping_bookable_from_another_store',
                'bookable_from_another_store',
            ]

            if (this.mapped_products.every((p) => DELIVERED_STATUSES.includes(p._availability_status))) {
                return { label: 'Expédiée / Retirée', color: 'green' }
            }

            if (this.mapped_products.every((p) => DELIVERABLE_STATUSES.includes(p._availability_status))) {
                return { label: 'Livrable', color: 'green' }
            }

            if (this.mapped_products.every((p) => BOOKABLE_STATUSES.includes(p._availability_status))) {
                return { label: 'Réservable', color: 'green' }
            }

            return { label: 'Non livrable', color: 'red' }
        },
    },
    created() {
        this.fetchData()
    },
    methods: {
        /**
         * Fetch all customer order products
         */
        async fetchData() {
            try {
                this.is_loading = true
                const response = await cPostCustomerOrderProducts(this.params)
                this.products = Object.freeze(response.data.customer_order_products).filter((product) => {
                    return product.availability[0].customer_order_product_quantity > 0
                })
            } catch (error) {
                useToastStore().add({ content: this.$i18n.t('error_message:request_error') })
            } finally {
                this.is_loading = false
            }
        },
        /**
         * 4 POSSIBLE STATUSES for a product from customer order with delivery :
         * delivered : the product has already been sent
         * available : the product is available and can be sent
         * replenishment : there is a supplier order in progress with date
         * unknown : we have no idea when the product will be replenished (with or without a knowing supplier order)
         *
         * 6 POSSIBLE STATUSES for a product from customer order with pickup from store :
         * delivered_from_store : the product has already been picked up from store
         * available_from_store : the product is in store and can be picked up
         * available_in_other_warehouse : the product is available for this customer order but still in stock in another warehouse or store (even with a transfer still not sent)
         * in_transfer : the product is available for this customer order and a transfer from another warehouse or store has already been sent
         * replenishment : there is a supplier order in progress with date
         * unknown : we have no idea when the product will be replenished (with or without a knowing supplier order)
         */
        getProductAvailabilityStatus(product) {
            const AVAILABLE_DATE = '1990-01-01'
            const UNKNOWN_DATE = '2011-01-01'

            if (
                product.availability[0].availability_date === AVAILABLE_DATE &&
                product.availability[0].store_pickup_id === null &&
                product.availability[0].customer_order_product_quantity === product.availability[0].sent_quantity
            ) {
                return 'delivered'
            }
            if (
                product.availability[0].availability_date === AVAILABLE_DATE &&
                product.availability[0].store_pickup_id !== null &&
                product.availability[0].customer_order_product_quantity === product.availability[0].sent_quantity
            ) {
                return 'delivered_from_store'
            }
            if (
                product.availability[0].customer_order_status === 'valid' &&
                product.availability[0].availability_date === AVAILABLE_DATE &&
                product.availability[0].store_pickup_id === null &&
                product.availability[0].customer_order_product_quantity !== product.availability[0].sent_quantity &&
                product.availability[0].warehouses_stock >= product.availability[0].customer_order_product_quantity
            ) {
                return 'available'
            }
            if (
                product.availability[0].customer_order_status === 'invalid' &&
                product.availability[0].availability_date === AVAILABLE_DATE &&
                product.availability[0].store_pickup_id === null &&
                product.availability[0].customer_order_product_quantity !== product.availability[0].sent_quantity &&
                product.availability[0].warehouses_stock >= product.availability[0].customer_order_product_quantity
            ) {
                return 'bookable'
            }
            if (
                product.availability[0].customer_order_status === 'valid' &&
                product.availability[0].availability_date === AVAILABLE_DATE &&
                product.availability[0].store_pickup_id === null &&
                product.availability[0].customer_order_product_quantity !== product.availability[0].sent_quantity &&
                product.availability[0].warehouses_stock < product.availability[0].customer_order_product_quantity
            ) {
                return 'shipping_available_in_another_store'
            }
            if (
                product.availability[0].customer_order_status === 'invalid' &&
                product.availability[0].availability_date === AVAILABLE_DATE &&
                product.availability[0].store_pickup_id === null &&
                product.availability[0].customer_order_product_quantity !== product.availability[0].sent_quantity &&
                product.availability[0].warehouses_stock < product.availability[0].customer_order_product_quantity
            ) {
                return 'shipping_bookable_from_another_store'
            }
            if (
                product.availability[0].customer_order_status === 'valid' &&
                product.availability[0].availability_date === AVAILABLE_DATE &&
                product.availability[0].store_pickup_id !== null &&
                product.availability[0].available_quantity_in_store >=
                    product.availability[0].customer_order_product_quantity
            ) {
                return 'available_in_store'
            }
            if (
                product.availability[0].customer_order_status === 'invalid' &&
                product.availability[0].availability_date === AVAILABLE_DATE &&
                product.availability[0].store_pickup_id !== null &&
                product.availability[0].available_quantity_in_store >=
                    product.availability[0].customer_order_product_quantity
            ) {
                return 'bookable'
            }
            if (
                product.availability[0].customer_order_status === 'valid' &&
                product.availability[0].availability_date === AVAILABLE_DATE &&
                product.availability[0].store_pickup_id !== null &&
                product.availability[0].store_quantity !== product.availability[0].customer_order_product_quantity &&
                product.availability[0].warehouses_stock >= product.availability[0].customer_order_product_quantity &&
                (product.available_transfers === null ||
                    !product.available_transfers.find((transfer) => transfer.status === 'expedie'))
            ) {
                return 'available'
            }
            if (
                product.availability[0].customer_order_status === 'valid' &&
                product.availability[0].availability_date === AVAILABLE_DATE &&
                product.availability[0].store_pickup_id !== null &&
                product.availability[0].store_quantity !== product.availability[0].customer_order_product_quantity &&
                product.availability[0].warehouses_stock < product.availability[0].customer_order_product_quantity &&
                (product.available_transfers === null ||
                    !product.available_transfers.find((transfer) => transfer.status === 'expedie'))
            ) {
                return 'available_in_another_store'
            }
            if (
                product.availability[0].customer_order_status === 'invalid' &&
                product.availability[0].availability_date === AVAILABLE_DATE &&
                product.availability[0].store_pickup_id !== null &&
                product.availability[0].store_quantity !== product.availability[0].customer_order_product_quantity &&
                (product.available_transfers === null ||
                    !product.available_transfers.find((transfer) => transfer.status === 'expedie'))
            ) {
                return 'bookable_from_another_store'
            }
            if (
                product.availability[0].store_pickup_id !== null &&
                product.available_transfers !== null &&
                product.available_transfers.find((transfer) => transfer.status === 'expedie')
            ) {
                return 'in_transfer'
            }
            if (
                product.availability[0].availability_date === UNKNOWN_DATE &&
                product.available_supplier_orders !== null
            ) {
                return 'unknown'
            }
            if (product.availability[0].availability_date === null) {
                return 'unknown'
            }

            return 'replenishment'
        },
        /**
         * Return object {label and color} from const
         */
        getProductAvailability(mapped_product) {
            return AVAILABILITY_STATUSES[mapped_product._availability_status]
        },
    },
}
</script>
