import { i18n } from '@/services/config/i18n'
import { useToastStore } from '@/services/plugin/toast/stores'
import { cPostCompetitors } from '@/shared/api/erp_server/competitor/cPostCompetitors'
import { ref } from 'vue'
import { getLowestCompetitorPrices } from '@/shared/api/erp_server/competitor_pricing/getLowestCompetitorPrices'
import type { CompetitorPricing } from '@/shared/api/erp_server/competitor_pricing/type'
import type { Competitor } from '@/shared/api/erp_server/competitor/types'
import { postCompetitorExclusion } from '@/shared/api/erp_server/competitor_pricing/postCompetitorExclusion'

const is_loading_competitors = ref(false)
const competitor_pricings = ref<CompetitorPricing[]>([])
const competitors = ref<Competitor[]>([])

async function fetchCompetitors() {
    try {
        is_loading_competitors.value = true
        const response = await cPostCompetitors({})
        competitors.value = response.data.competitors
    } catch (error) {
        useToastStore().add({ content: i18n.t('error_message:request_error') })
    } finally {
        is_loading_competitors.value = false
    }
}

async function fetchArticleLowestCompetitors(article_id: number) {
    is_loading_competitors.value = true
    try {
        const response = await getLowestCompetitorPrices(article_id)

        const competitor_pricings_array: CompetitorPricing[] = []
        let i = 0
        for (const competitor_price of response.data.competitor_prices) {
            const is_in_array = competitor_pricings_array.find((entry) => competitor_price.site == entry.site)
            if (!is_in_array) {
                const competitor_pricing = competitor_price
                competitor_pricing.id = i++
                competitor_pricing.url = anonymizeURL(competitor_pricing.url)
                competitor_pricing.other_competitors = response.data.competitor_prices
                    .filter((entry) => entry.site === competitor_price.site && competitor_price != entry)
                    .map((competitor) => ({
                        site: competitor.site,
                        competitor_code: competitor.competitor_code,
                        selling_price_with_taxes: competitor.selling_price_with_taxes,
                        is_available: competitor.is_available,
                        crawled_at: competitor.crawled_at,
                        url: anonymizeURL(competitor.url),
                        is_ignored: competitor.is_ignored,
                        ignore_until: competitor.ignore_until,
                    }))
                competitor_pricings_array.push(competitor_pricing)
            }
        }
        competitor_pricings.value = competitor_pricings_array
    } catch (error: any) {
        competitor_pricings.value = []
    } finally {
        is_loading_competitors.value = false
    }
}

async function addCompetitorExclusion(competitor_code: string, article_id: number) {
    try {
        await postCompetitorExclusion(competitor_code, article_id)
    } catch (error: any) {
        useToastStore().add({ content: i18n.t('error_message:request_error') })
    }
}

const anonymizeURL = (url: string) => {
    url = encodeURIComponent(url)
    return 'https://dereferer.me/?' + url
}

export const useCompetitors = () => {
    return {
        fetchCompetitors,
        addCompetitorExclusion,
        fetchArticleLowestCompetitors,
        is_loading_competitors,
        competitors,
        competitor_pricings,
    }
}
