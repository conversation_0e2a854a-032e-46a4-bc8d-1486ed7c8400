import { NON_BREAKING_SPACE } from '../../utils/text-utils'

describe('Customer order products margin', () => {
    const VISIT_PAGE = (file) => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {},
            },
        }).as('customer_order')

        cy.intercept('POST', '**/api/erp/v1/customer-order-products', { fixture: file }).as(
            'cpost_customer_order_products',
        )

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.emitEvent('open-products-margin', 123456)
        cy.wait('@customer_order')
        cy.wait('@cpost_customer_order_products')
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Displays customer order products margin', () => {
        it('displays products margin with correct calculations', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .should('exist')
            cy.get('@panel').find('[data-context=page-header]').should('contain', 'Marges produits')

            cy.closeAllToasts()

            // Check table header
            cy.get('@panel').find('[data-context=erp-table]').as('table')
            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 6)
            cy.get('@header').eq(0).should('contain', 'Produit')
            cy.get('@header').eq(1).should('contain', 'Qté')
            cy.get('@header').eq(2).should('contain', 'Prix de vente TTC')
            cy.get('@header').eq(3).should('contain', 'Remise TTC')
            cy.get('@header').eq(4).should('contain', 'Marge TTC')

            // Check products rows (3 products + 1 total row = 4 rows)
            cy.get('@panel').find('[data-context=table-row]').should('have.length', 4)
        })

        it('displays first product margin details correctly', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .should('exist')

            // First product: S809 HCS Bois noir
            cy.get('@panel').find('[data-context=table-row]').eq(0).as('product1')
            cy.get('@product1')
                .find('[data-context=sku]')
                .should('contain', 'JAMOS809HCSBNR')
                .should('have.attr', 'href', '/articles/JAMOS809HCSBNR/')
            cy.get('@product1')
                .find('[data-context=name]')
                .should('contain', 'S809 HCS Bois noir')
            cy.get('@product1')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS809HCSBNR/s809-hcs-bois-noir_5a5f346ee9538_300_square.jpg')
            
            // Check calculated values for quantity 2
            cy.get('@product1').should('contain', '2') // quantity
            cy.get('@product1').should('contain', '599,98' + NON_BREAKING_SPACE + '€') // selling price: 299.99 * 2
            cy.get('@product1').should('contain', '15,50' + NON_BREAKING_SPACE + '€') // discount
            cy.get('@product1').should('contain', '190,50' + NON_BREAKING_SPACE + '€') // margin: 95.25 * 2
        })

        it('displays second product margin details correctly', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .should('exist')

            // Second product: S810 SUB Bois noir
            cy.get('@panel').find('[data-context=table-row]').eq(1).as('product2')
            cy.get('@product2')
                .find('[data-context=sku]')
                .should('contain', 'JAMOS810SUBNR')
                .should('have.attr', 'href', '/articles/JAMOS810SUBNR/')
            cy.get('@product2')
                .find('[data-context=name]')
                .should('contain', 'S810 SUB Bois noir')
            cy.get('@product2')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            
            // Check calculated values for quantity 1
            cy.get('@product2').should('contain', '1') // quantity
            cy.get('@product2').should('contain', '199,99' + NON_BREAKING_SPACE + '€') // selling price: 199.99 * 1
            cy.get('@product2').should('contain', '8,00' + NON_BREAKING_SPACE + '€') // discount
            cy.get('@product2').should('contain', '65,50' + NON_BREAKING_SPACE + '€') // margin: 65.50 * 1
        })

        it('displays third product margin details correctly', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .should('exist')

            // Third product: Câble HDMI 2m
            cy.get('@panel').find('[data-context=table-row]').eq(2).as('product3')
            cy.get('@product3')
                .find('[data-context=sku]')
                .should('contain', 'CABHDMI2M')
                .should('have.attr', 'href', '/articles/CABHDMI2M/')
            cy.get('@product3')
                .find('[data-context=name]')
                .should('contain', 'Câble HDMI 2m')
            cy.get('@product3')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/generic/CABHDMI2M/cable-hdmi-2m_300_square.jpg')
            
            // Check calculated values for quantity 3
            cy.get('@product3').should('contain', '3') // quantity
            cy.get('@product3').should('contain', '38,97' + NON_BREAKING_SPACE + '€') // selling price: 12.99 * 3
            cy.get('@product3').should('contain', '2,50' + NON_BREAKING_SPACE + '€') // discount
            cy.get('@product3').should('contain', '23,55' + NON_BREAKING_SPACE + '€') // margin: 7.85 * 3
        })

        it('displays total row with correct calculations and styling', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .should('exist')

            // Total row (last row)
            cy.get('@panel').find('[data-context=table-row]').eq(3).as('total')
            cy.get('@total').should('contain', 'TOTAL')
            cy.get('@total').find('.font-bold.text-gray-900').should('exist')
            
            // Check total calculations
            cy.get('@total').should('contain', '6') // total quantity: 2 + 1 + 3
            cy.get('@total').should('contain', '838,94' + NON_BREAKING_SPACE + '€') // total selling price: 599.98 + 199.99 + 38.97
            cy.get('@total').should('contain', '26,00' + NON_BREAKING_SPACE + '€') // total discount: 15.50 + 8.00 + 2.50
            cy.get('@total').should('contain', '279,55' + NON_BREAKING_SPACE + '€') // total margin: 190.50 + 65.50 + 23.55
        })

        it('displays correct currency formatting', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .should('exist')

            // Check that all currency values are properly formatted with € symbol and French formatting
            cy.get('@panel').should('contain', '599,98' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '199,99' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '38,97' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '838,94' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '15,50' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '8,00' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '2,50' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '26,00' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '190,50' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '65,50' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '23,55' + NON_BREAKING_SPACE + '€')
            cy.get('@panel').should('contain', '279,55' + NON_BREAKING_SPACE + '€')
        })

        it('does not display products with zero margin details', () => {
            // This test would use a fixture with products that have empty margin_details
            // to ensure they are filtered out correctly
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .should('exist')

            // All 3 products should be displayed since they all have margin_details
            cy.get('@panel').find('[data-context=table-row]').should('have.length', 4) // 3 products + 1 total
        })

        it('handles empty product list', () => {
            cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
                statusCode: 200,
                body: {
                    data: {},
                },
            }).as('customer_order_empty')

            cy.intercept('POST', '**/api/erp/v1/customer-order-products', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        customer_order_products: [],
                    },
                },
            }).as('cpost_customer_order_products_empty')

            cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')
            cy.emitEvent('open-products-margin', 123456)
            cy.wait('@customer_order_empty')
            cy.wait('@cpost_customer_order_products_empty')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .should('exist')
            cy.get('@panel').find('[data-context=page-header]').should('contain', 'Marges produits')
            cy.get('@panel').find('[data-context=table-row]').should('not.exist')
        })

        it('handles API error gracefully', () => {
            cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
                statusCode: 200,
                body: {
                    data: {},
                },
            }).as('customer_order_error')

            cy.intercept('POST', '**/api/erp/v1/customer-order-products', {
                statusCode: 500,
                body: { error: 'Internal server error' },
            }).as('cpost_customer_order_products_error')

            cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')
            cy.emitEvent('open-products-margin', 123456)
            cy.wait('@customer_order_error')
            cy.wait('@cpost_customer_order_products_error')

            // Should display error toast
            cy.get('[data-context=toast-container]').should('exist')
            cy.get('[data-context=toast]').should('contain', 'Une erreur est survenue lors de la récupération des données')
        })
    })
})
