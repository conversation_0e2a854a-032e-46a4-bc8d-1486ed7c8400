<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { cPostCustomerOrderProducts, type CustomerOrderProduct } from '@/shared/api/erp_server'
import { useToastStore } from '@/services/plugin/toast/stores'
import { ErpTable } from '@/shared/components/ErpTable'
import PageHeader from '@/shared/components/ui/PageHeader.vue'
import ErpArticleItem from '@/shared/components/article/ErpArticleItem.vue'
import { formatCurrency } from '@/shared/string_utils'
import { useCustomerOrderStore } from '@/apps/erp/components/CustomerOrder/stores/customer_order'
import { storeToRefs } from 'pinia'

const store = useCustomerOrderStore()
const { customer_order } = storeToRefs(store)

interface Props {
    customerOrderId: number
}

interface CustomerOrderProductMargin {
    customer_order_product_id: number
    article_name: string
    article_image: string
    sku: string
    quantity: number
    selling_price_tax_included: number
    margin_rate: number
    unit_selling_price_tax_included: number
}

const props = withDefaults(defineProps<Props>(), {})

const is_loading = ref<boolean>(false)
const products = ref<CustomerOrderProduct[]>([])

const columns = ref([
    {
        name: 'article',
        title: 'Produit',
    },
    {
        name: 'quantity',
        title: 'Quantité',
        _columnClasses: 'whitespace-nowrap text-right',
    },
    {
        name: 'unit_selling_price',
        title: 'Prix Un. TTC',
        _columnClasses: 'whitespace-nowrap text-right',
    },
    {
        name: 'selling_price',
        title: 'Prix TTC',
        _columnClasses: 'whitespace-nowrap text-right',
    },
    {
        name: 'margin_rate',
        title: 'Marge',
        _columnClasses: 'whitespace-nowrap text-right',
    },
    {
        name: 'dummy',
        title: '',
    },
])

const params = computed(() => {
    return {
        where: {
            _and: [
                {
                    customer_order_id: {
                        _eq: props.customerOrderId,
                    },
                },
            ],
        },
        included_dependencies: ['margin_details'],
        limit: 9999,
    }
})

const products_with_warranties = computed<CustomerOrderProductMargin[]>(() => {
    return products.value.reduce((acc: CustomerOrderProductMargin[], curr: CustomerOrderProduct) => {
        const unit_discount_amount = Math.abs(curr.discount_amount * curr.quantity)
        acc.push({
            article_image: curr.article_image,
            sku: curr.sku,
            customer_order_product_id: curr.customer_order_product_id,
            article_name: curr.article_name,
            quantity: curr.quantity,
            selling_price_tax_included:
                (curr.margin_details[0].selling_price_tax_included - unit_discount_amount) * curr.quantity,
            margin_rate: curr.margin_details[0].margin_rate,
            unit_selling_price_tax_included: curr.margin_details[0].selling_price_tax_included - unit_discount_amount,
        })

        if (curr.extension_warranty_price !== 0) {
            acc.push({
                article_image: '',
                sku: 'warranty',
                customer_order_product_id: -1,
                article_name: 'Garantie premium ' + curr.extension_warranty_duration + ' ans',
                quantity: curr.quantity,
                selling_price_tax_included: curr.extension_warranty_price * curr.quantity,
                margin_rate: 0,
                unit_selling_price_tax_included: curr.extension_warranty_price,
            })
        }
        if (curr.damage_and_theft_warranty_price !== 0) {
            acc.push({
                article_image: '',
                sku: 'warranty',
                customer_order_product_id: -1,
                article_name: 'Garantie vol-casse ' + curr.damage_and_theft_warranty_duration + ' ans',
                quantity: curr.quantity,
                selling_price_tax_included: curr.damage_and_theft_warranty_price * curr.quantity,
                margin_rate: 0,
                unit_selling_price_tax_included: curr.damage_and_theft_warranty_price,
            })
        }

        return acc
    }, [])
})

// Computed property pour ajouter une ligne de total
const computed_products = computed<CustomerOrderProductMargin[]>(() => {
    if (!products.value || products.value.length === 0) return []

    // Add shipping cost
    products_with_warranties.value.push({
        article_image: '',
        sku: 'shipping',
        customer_order_product_id: -1,
        article_name: 'Frais de port',
        quantity: 1,
        selling_price_tax_included: parseFloat(customer_order.value?.shipping_price ?? ''),
        margin_rate: 0,
        unit_selling_price_tax_included: parseFloat(customer_order.value?.shipping_price ?? ''),
    })

    // Calculer les totaux
    const totals = products_with_warranties.value.reduce(
        (acc, product) => {
            return {
                quantity: acc.quantity + product.quantity,
                selling_price_tax_included: acc.selling_price_tax_included + product.selling_price_tax_included,
            }
        },
        {
            quantity: 0,
            selling_price_tax_included: 0,
        },
    )

    // Créer l'élément de total
    const total_row: CustomerOrderProductMargin = {
        article_image: '',
        sku: 'total',
        customer_order_product_id: -1, // ID spécial pour identifier la ligne de total
        article_name: 'TOTAL', // ID spécial pour identifier la ligne de total
        quantity: totals.quantity,
        selling_price_tax_included: totals.selling_price_tax_included,
        unit_selling_price_tax_included: 0,
        margin_rate: 0,
    }

    return [...products_with_warranties.value, total_row]
})

/**
 * Fetch all customer order products
 */
const fetchData = async (): Promise<void> => {
    try {
        is_loading.value = true
        const response = await cPostCustomerOrderProducts(params.value)
        products.value = response.data.customer_order_products
    } catch (error) {
        useToastStore().add({ content: 'Une erreur est survenue lors de la récupération des données' })
    } finally {
        is_loading.value = false
    }
}

onMounted(() => {
    fetchData()
})
</script>

<template>
    <div v-if="!is_loading" data-context="products-margin">
        <page-header>
            <span class="mr-3">Marges produits</span>
        </page-header>

        <erp-table :columns="columns" :rows="computed_products">
            <template #article="{ row }" class="flex items-center gap-3 ml-10">
                <div v-if="row.sku === 'total'" class="font-bold text-gray-900">
                    {{ row.article_name }}
                </div>
                <div v-else-if="row.sku === 'warranty'" class="flex items-center gap-3 ml-3">
                    <font-awesome-icon fixed-width :icon="['fad', 'shield-check']" class="text-green-600 text-2xl" />
                    <div class="text-green-900">{{ row.article_name }}</div>
                </div>
                <div v-else-if="row.sku === 'shipping'" class="flex items-center gap-3 ml-4">
                    <font-awesome-icon fixed-width :icon="['fad', 'truck']" class="text-blue-500 text-xl" />
                    <div class="text-blue-900">{{ row.article_name }}</div>
                </div>
                <erp-article-item
                    v-else
                    show-actions
                    :sku="row.sku"
                    :name="row.article_name"
                    :image="row.article_image"
                />
            </template>

            <template #quantity="{ row }">
                <div :class="{ 'font-bold text-gray-900': row.article_name === 'TOTAL' }">
                    {{ row.quantity }}
                </div>
            </template>

            <template #unit_selling_price="{ row }">
                <div
                    :class="{ 'font-bold text-gray-900': row.article_name === 'TOTAL' }"
                    v-if="!['warranty', 'total'].includes(row.article_name)"
                >
                    {{ formatCurrency(row.unit_selling_price_tax_included) }}
                </div>
                <div v-else class="font-bold text-gray-900">-</div>
            </template>

            <template #selling_price="{ row }">
                <div :class="{ 'font-bold text-gray-900': row.article_name === 'TOTAL' }">
                    {{ formatCurrency(row.selling_price_tax_included) }}
                </div>
            </template>

            <template #margin_rate="{ row }">
                <span v-if="!['warranty', 'total', 'shipping'].includes(row.sku)"> {{ row.margin_rate }} % </span>
                <span v-else>-</span>
            </template>
            <template #dummy>
                <span></span>
            </template>
        </erp-table>
    </div>
</template>
