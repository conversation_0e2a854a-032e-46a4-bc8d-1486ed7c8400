describe('Customer order products margin', () => {
    const VISIT_PAGE = (file) => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {},
            },
        }).as('customer_order')

        cy.intercept('POST', '**/api/erp/v1/customer-order-products', { fixture: file }).as(
            'cpost_customer_order_products',
        )

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.emitEvent('open-products-margin', 123456)
        cy.wait('@customer_order')
        cy.wait('@cpost_customer_order_products')
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Displays customer order products margins', () => {})
})
