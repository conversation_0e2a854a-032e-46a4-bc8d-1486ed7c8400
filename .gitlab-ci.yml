# Shared YAML config pulled from another repo
include:
  - project: 'son-video/support'
    ref: master
    file: '/.gitlab-ci-template.yml'

workflow:
  rules:
    # pipeline should only run for merge requests, scheduled pipelines or triggered manually from the Gitlab CI pipeline page
    - if: $CI_PIPELINE_SOURCE =~ /^merge_request_event|schedule|web$/
    # or for branch `master and validation`.
    - if: $CI_COMMIT_BRANCH =~ /^(master|validation)$/

default:
  image: registry.gitlab.com/son-video/docker-stack/node:18.16.1-chrome-114
  interruptible: true
  tags:
    - saas-linux-small-amd64

## Set environment variables for folders in "cache" job settings for npm modules and Cypress binary
variables:
  # Recommendations from Cypress.io
  npm_config_cache: '$CI_PROJECT_DIR/.npm'
  CYPRESS_CACHE_FOLDER: '$CI_PROJECT_DIR/cache/Cypress'
  # Filesystem for docker jobs
  DOCKER_DRIVER: overlay2
  # Files shared between jobs
  ARTIFACTS_DIR: $CI_PROJECT_DIR/artifacts
  CI_TESTS_STAGE:
    value: 'EXECUTE'
    description: "Indicates the action to do regarding tests"
    options:
      - 'EXECUTE'
      - 'SKIP'
  CYPRESS_FAIL_FAST_ENABLED:
    value: 'true'
    description: "Activate the 'fail fast' strategy for the run"
    options:
      - 'true'
      - 'false'
  INSTALL_DEPENDENCIES:
    value: 'DEFAULT'
    description: "REGENERATE the CI rule regarding the generation of the dependencies (cached by default)"
    options:
      - 'DEFAULT'
      - 'REGENERATE'

stages:
  - install
  - build
  - test
  - deploy

# same package-lock hash should always use the same node_modules
.cache-node: &cache_node
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/
    - cache/Cypress/

.run-test:
  rules:
    - if: $CI_TESTS_STAGE != "SKIP" && $CI_COMMIT_BRANCH != "validation"
  cache:
    - <<: *cache_node
      policy: pull


#####################################
# Install Stage
#####################################

install-node-dependencies:
  stage: install
  rules:
    - if: $INSTALL_DEPENDENCIES == "REGENERATE"
    # trigger this job only if package-lock has changed, or manual (see key ruleschanges in doc)
    - changes:
        - package-lock.json
  cache:
    - <<: *cache_node
      policy: pull-push
    # store dependency managers' caches for all branches
    - key: ${CI_JOB_NAME}
      # must be inside $CI_PROJECT_DIR for gitlab-runner caching
      paths:
        - .npm/ # used as global cache when running "npm ci"
      policy: pull-push
  script:
    # exit if cache already contains the vendor dir
    - if [[ "$INSTALL_DEPENDENCIES" != "REGENERATE" ]]; then test -d node_modules && exit 10; fi
    - if [[ "$INSTALL_DEPENDENCIES" == "REGENERATE" ]]; then echo "REGENERATE node dependencies has been forced from the UI"; fi
    # use a global cache directory in the project in order to let gitlab access it
    - npm ci --cache .npm --prefer-offline
  allow_failure:
    exit_codes: 10


#####################################
# Build Stage
#####################################

# build used by "test" stage
build-test:
  stage: build
  extends:
    - .run-test
  before_script:
    - mkdir -p $ARTIFACTS_DIR
  script:
    - npm run build:e2e
    - cp -r dist $ARTIFACTS_DIR/
  artifacts:
    expire_in: 1 day
    paths:
      - $ARTIFACTS_DIR/

# builds used by "deploy" stage
.build-deploy-base:
  stage: build
  cache:
    - <<: *cache_node
      policy: pull
  before_script:
    - mkdir -p $ARTIFACTS_DIR

build-validation:
  rules:
    - if: $CI_COMMIT_BRANCH == "validation"
  extends:
    - .build-deploy-base
  script:
    - cd deployment && make bump-patch-version-validation package-val
    - cp erp-client-val.tar $ARTIFACTS_DIR/erp-client-val.tar
  artifacts:
    expire_in: 1 day
    paths:
      - $ARTIFACTS_DIR/

build-staging:
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
  extends:
    - .build-deploy-base
  script:
    - cd deployment && make bump-patch-version package-stg
    - cp erp-client-stg.tar $ARTIFACTS_DIR/erp-client-stg.tar
  artifacts:
    expire_in: 1 day
    paths:
      - $ARTIFACTS_DIR/

build-prod:
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
  extends:
    - .build-deploy-base
  script:
    - cd deployment && make package
    - cp erp-client-prd.tar $ARTIFACTS_DIR/erp-client-prd.tar
  artifacts:
    expire_in: 1 day
    paths:
      - $ARTIFACTS_DIR/

#####################################
# Test Stage
#####################################

# Unit tests (Which require the node docker image to have the French locale installed)
vitest:
  stage: test
  extends:
    - .run-test
  dependencies: []
  script:
    - npm run test:unit

# Run cypress tests (not in parallel)
cypress:
  stage: test
  extends:
    - .run-test
  dependencies:
    - build-test
  parallel: 3
  before_script:
    # restore artifacts
    - cp -r $ARTIFACTS_DIR/dist .
  script:
    # Prevent inotify error
    - echo fs.inotify.max_user_watches=524288 | tee -a /etc/sysctl.conf && sysctl -p

    # Run cypress tests
    - npm run e2e:ci
  artifacts:
    when: always
    paths:
      - tests/e2e/results/html
      - tests/e2e/screenshots
    reports:
      junit:
        - tests/e2e/results/results.xml
    expire_in: 1 day

# Run cypress component tests (not in parallel)
# deactivated due to cypress messing around
cypress-component:
  stage: test
  extends:
    - .run-test
  dependencies: []
  script:
    # Prevent inotify error
    - echo fs.inotify.max_user_watches=524288 | tee -a /etc/sysctl.conf && sysctl -p

    # Run cypress tests
    - npm run component
  artifacts:
    when: always
    paths:
      - tests/e2e/results/html
      - tests/e2e/screenshots
    reports:
      junit:
        - tests/e2e/results/results.xml
    expire_in: 1 day


#####################################
# Deploy Stage
#####################################
.deploy-stage:
  stage: deploy
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  interruptible: false
  extends:
    - .with-ssh
  script: &deployment_scripts
    - apt-get update && apt-get install -y make git

    - git config --global user.name "${GITLAB_USER_NAME}"
    - git config --global user.email "${GITLAB_USER_EMAIL}"
    - git remote set-url --<NAME_EMAIL>:son-video/erp-client.git
    - git fetch -p --all --tags --force

deploy-validation:
  rules:
    - if: $CI_COMMIT_BRANCH == "validation"
  extends: .deploy-stage
  dependencies:
    - build-validation
  script:
    - *deployment_scripts
    - cp $ARTIFACTS_DIR/erp-client-val.tar deployment/erp-client-val.tar
    - cd deployment && make skip-some-recipes deploy-validation

deploy-staging-and-prod:
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
  extends: .deploy-stage
  dependencies:
    - build-staging
    - build-prod
  script:
    - *deployment_scripts
    - cp $ARTIFACTS_DIR/erp-client-stg.tar deployment/erp-client-stg.tar
    - cp $ARTIFACTS_DIR/erp-client-prd.tar deployment/erp-client-prd.tar
    - cd deployment && make skip-some-recipes deploy
