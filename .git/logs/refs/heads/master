bf01cc441e9d5d0297b1ec3e1b942d559f1e9ead d518c2dfca46ec8b629befc2f27f47fac0d446bf RomainM <romain.mabil<PERSON>@son-video.com> 1735922915 +0100	pull origin: Fast-forward
d518c2dfca46ec8b629befc2f27f47fac0d446bf 3bf8ebc7e6c607cc259b320c6be0cff15cb4f88a RomainM <<EMAIL>> 1736329419 +0100	rebase (finish): refs/heads/master onto 3bf8ebc7e6c607cc259b320c6be0cff15cb4f88a
3bf8ebc7e6c607cc259b320c6be0cff15cb4f88a 4c520c025511dd050e234aaff923d769f4308178 RomainM <<EMAIL>> 1736852965 +0100	fetch origin refs/heads/master:refs/heads/master --recurse-submodules=no --progress --prune: fast-forward
4c520c025511dd050e234aaff923d769f4308178 ce080821e16773c6340b7cd51e9818079a325961 RomainM <<EMAIL>> 1737018960 +0100	pull origin: Fast-forward
ce080821e16773c6340b7cd51e9818079a325961 9c8150e4488a783b004be987bdff1bef9aac3d81 RomainM <<EMAIL>> 1737468659 +0100	fetch origin refs/heads/master:refs/heads/master --recurse-submodules=no --progress --prune: fast-forward
9c8150e4488a783b004be987bdff1bef9aac3d81 6035479dbbd08a96586d80b585f43db18fe6fe02 RomainM <<EMAIL>> 1737477420 +0100	rebase (finish): refs/heads/master onto 6035479dbbd08a96586d80b585f43db18fe6fe02
6035479dbbd08a96586d80b585f43db18fe6fe02 133857a415a9bd7d2612083c952a7b26baae451c RomainM <<EMAIL>> 1737987414 +0100	rebase (finish): refs/heads/master onto 133857a415a9bd7d2612083c952a7b26baae451c
133857a415a9bd7d2612083c952a7b26baae451c 1158d66640d2d2228f0ce21d9e17079c9f44d002 RomainM <<EMAIL>> 1738593069 +0100	pull origin: Fast-forward
1158d66640d2d2228f0ce21d9e17079c9f44d002 0e650cc09dbd5c435bc692a22917730acd4516fb RomainM <<EMAIL>> 1740394392 +0100	rebase (finish): refs/heads/master onto 0e650cc09dbd5c435bc692a22917730acd4516fb
0e650cc09dbd5c435bc692a22917730acd4516fb 787b9967096b8d90015e4ef564b621a58b03bd35 RomainM <<EMAIL>> 1741264537 +0100	rebase (finish): refs/heads/master onto 787b9967096b8d90015e4ef564b621a58b03bd35
787b9967096b8d90015e4ef564b621a58b03bd35 08f4de9da049e2bbdd951e857f52238d04c49f61 RomainM <<EMAIL>> 1741614050 +0100	fetch origin refs/heads/master:refs/heads/master --recurse-submodules=no --progress --prune: fast-forward
08f4de9da049e2bbdd951e857f52238d04c49f61 d073bad587879bbdaecf09f9e6acb35bbc99fa47 RomainM <<EMAIL>> 1741624783 +0100	rebase (finish): refs/heads/master onto d073bad587879bbdaecf09f9e6acb35bbc99fa47
d073bad587879bbdaecf09f9e6acb35bbc99fa47 5455bba575988be57912cd42a4dd30041a690faa RomainM <<EMAIL>> 1741625029 +0100	commit: hotfix: merge problem
5455bba575988be57912cd42a4dd30041a690faa a10620da24f2c572102569ce7efa72206125e697 RomainM <<EMAIL>> 1741713089 +0100	rebase (finish): refs/heads/master onto a10620da24f2c572102569ce7efa72206125e697
a10620da24f2c572102569ce7efa72206125e697 f615316856b33fd941bbdf8e32808907238a018b RomainM <<EMAIL>> 1741862940 +0100	rebase (finish): refs/heads/master onto f615316856b33fd941bbdf8e32808907238a018b
f615316856b33fd941bbdf8e32808907238a018b b00a3698efa192b858af39552c9c0c71fdfc3755 RomainM <<EMAIL>> 1742308376 +0100	fetch origin refs/heads/master:refs/heads/master --recurse-submodules=no --progress --prune: fast-forward
b00a3698efa192b858af39552c9c0c71fdfc3755 df90e6b9b9c375ca838e28651e8db349ef4f9b34 RomainM <<EMAIL>> 1742485743 +0100	rebase (finish): refs/heads/master onto df90e6b9b9c375ca838e28651e8db349ef4f9b34
df90e6b9b9c375ca838e28651e8db349ef4f9b34 cb8980b8a8e40e02674f85f2c446a40b0018293b RomainM <<EMAIL>> 1742809320 +0100	rebase (finish): refs/heads/master onto cb8980b8a8e40e02674f85f2c446a40b0018293b
cb8980b8a8e40e02674f85f2c446a40b0018293b a20ed0a7070475ea52c8dc1514c6670e9014fd91 RomainM <<EMAIL>> 1742812644 +0100	commit: hotfix: wrong stock value retrieved when loading an article
a20ed0a7070475ea52c8dc1514c6670e9014fd91 1db27b9838accb5fb4c62bce0d23d314abc1ad2b RomainM <<EMAIL>> 1742999609 +0100	rebase (finish): refs/heads/master onto 1db27b9838accb5fb4c62bce0d23d314abc1ad2b
1db27b9838accb5fb4c62bce0d23d314abc1ad2b 6eba3461d5985e6f0471a833581f1782eb2d6132 RomainM <<EMAIL>> 1742999614 +0100	merge refs/heads/article_v2/subtabs_localstorage: Merge made by the 'ort' strategy.
6eba3461d5985e6f0471a833581f1782eb2d6132 c8162bae8bfbd903da1383f32ab1e5b5ab1c5692 RomainM <<EMAIL>> 1743429715 +0200	rebase (finish): refs/heads/master onto c8162bae8bfbd903da1383f32ab1e5b5ab1c5692
c8162bae8bfbd903da1383f32ab1e5b5ab1c5692 fb73667ba7055d81d4549fc9c68336968876e172 RomainM <<EMAIL>> 1743500047 +0200	merge origin/master: Fast-forward
fb73667ba7055d81d4549fc9c68336968876e172 71f170280c1b8a26914b2df6464eb377e6c37c7e RomainM <<EMAIL>> 1745330252 +0200	fetch origin refs/heads/master:refs/heads/master --recurse-submodules=no --progress --prune: fast-forward
71f170280c1b8a26914b2df6464eb377e6c37c7e 2b29ccafb8e79902a18f5a7e9c432dd85a28a661 RomainM <<EMAIL>> 1745420950 +0200	rebase (finish): refs/heads/master onto 2b29ccafb8e79902a18f5a7e9c432dd85a28a661
2b29ccafb8e79902a18f5a7e9c432dd85a28a661 f02eabb60605017596fe8fa6c9e20a6743e956d4 RomainM <<EMAIL>> 1745853795 +0200	rebase (finish): refs/heads/master onto f02eabb60605017596fe8fa6c9e20a6743e956d4
f02eabb60605017596fe8fa6c9e20a6743e956d4 6b7424affee47eff61ecca10ff7ab1211da15f6c RomainM <<EMAIL>> 1746454956 +0200	pull origin: Fast-forward
6b7424affee47eff61ecca10ff7ab1211da15f6c 3208207416d2390a49dce24cccce164cff9e0258 RomainM <<EMAIL>> 1747923787 +0200	rebase (finish): refs/heads/master onto 3208207416d2390a49dce24cccce164cff9e0258
3208207416d2390a49dce24cccce164cff9e0258 e79cd58efb58ab2ee3cb7b4cbab5b07e8085bbd1 RomainM <<EMAIL>> 1749127728 +0200	rebase (finish): refs/heads/master onto e79cd58efb58ab2ee3cb7b4cbab5b07e8085bbd1
e79cd58efb58ab2ee3cb7b4cbab5b07e8085bbd1 b1812f787e98c41183bed7a6e60e74f6cc554599 RomainM <<EMAIL>> 1750684364 +0200	rebase (finish): refs/heads/master onto b1812f787e98c41183bed7a6e60e74f6cc554599
b1812f787e98c41183bed7a6e60e74f6cc554599 da0e01fbb9a000a63cd1df5bb0bfcca5b533444d RomainM <<EMAIL>> 1752847473 +0200	rebase (finish): refs/heads/master onto da0e01fbb9a000a63cd1df5bb0bfcca5b533444d
da0e01fbb9a000a63cd1df5bb0bfcca5b533444d 4f9cbd22eaa2449a1f36b7c50e74db8eb9d22cfc RomainM <<EMAIL>> 1754038174 +0200	pull origin: Fast-forward
4f9cbd22eaa2449a1f36b7c50e74db8eb9d22cfc 4bfa56629ca611926de54710eaca40d16b270d29 RomainM <<EMAIL>> 1754495661 +0200	fetch origin master:master --recurse-submodules=no --progress --prune: fast-forward
4bfa56629ca611926de54710eaca40d16b270d29 676716a02cc062be00a0820a783266dcf470157d RomainM <<EMAIL>> 1754495677 +0200	commit: feat[support#2487]: dissociate permissions for sales channel
676716a02cc062be00a0820a783266dcf470157d 77fb66e8a29c85cad8f9e17a080f9f75a27feca5 RomainM <<EMAIL>> 1754499040 +0200	commit: feat[support#2487]: dissociate permissions for sales channel
77fb66e8a29c85cad8f9e17a080f9f75a27feca5 a1f78730f856e24a0e396d55953cde1d628ab025 RomainM <<EMAIL>> 1755524868 +0200	fetch origin master:master --recurse-submodules=no --progress --prune: fast-forward
a1f78730f856e24a0e396d55953cde1d628ab025 2741497faa588ab165ef66cf2c5ed081c22e662f RomainM <<EMAIL>> 1755763536 +0200	pull origin: Fast-forward
2741497faa588ab165ef66cf2c5ed081c22e662f a065d168037b6499f0cc670bb80cc20d0259fd4d RomainM <<EMAIL>> 1756393807 +0200	rebase (finish): refs/heads/master onto a065d168037b6499f0cc670bb80cc20d0259fd4d
a065d168037b6499f0cc670bb80cc20d0259fd4d 1e14a87908e0c14cc39eea85e3d348a700fbfeb8 RomainM <<EMAIL>> 1757345717 +0200	merge origin/master: Fast-forward
1e14a87908e0c14cc39eea85e3d348a700fbfeb8 388e9fe5bb20815f168f7806df6b2a29f6bf7148 RomainM <<EMAIL>> 1757692343 +0200	merge origin/master: Fast-forward
