<script lang="ts" setup>
import PaymentV2OperationActions from '@/apps/erp/components/CustomerOrder/Payment/PaymentV2OperationActions.vue'
import {
    useCustomerOrderPaymentV2Store,
    useCustomerOrderStore,
} from '@/apps/erp/components/CustomerOrder/stores/customer_order'
import WorldlineAddPayByLinkPayment from '@/apps/erp/components/CustomerOrder/Payment/WorldlineAddPayByLinkPayment.vue'
import { CUSTOMER_ORDER_PAYMENT_ADD } from '@/apps/erp/permissions'
import {
    type AntiFraudForCustomerOrderCustomerOrderPayments,
    AntiFraudReason,
    AntiFraudStatus,
} from '@/shared/api/erp_server/anti_fraud/types'
import {
    type CustomerOrderPayment,
    CustomerOrderPaymentStatus,
    CustomerOrderPaymentType,
    CustomerOrderPaymentWorkflow,
} from '@/shared/api/erp_server/customer_order/types'
import type { PaymentV2OperationDetails } from '@/shared/api/erp_server/payment/types'
import { Badge, ErpTable } from '@/shared/components'
import ErpButton from '@/shared/components/buttons/ErpButton.vue'
import CustomerOrderPaymentStatusBadge from '@/shared/components/customer_order/CustomerOrderPaymentStatusBadge.vue'
import { type ErpTableRow, STYLE_PRESETS } from '@/shared/components/ErpTable'
import Alert from '@/shared/components/ui/Alert.vue'
import ErpTooltip from '@/shared/components/ui/ErpTooltip.vue'
import { ONGOING } from '@/shared/referential/customer_order/status_types'
import { formatCurrency } from '@/shared/string_utils'
import { useAuthenticationStore } from '@/stores/authentication'
import { storeToRefs } from 'pinia'
import { computed, ref } from 'vue'
import {
    faAngleDoubleDown,
    faAngleDoubleUp,
    faExclamationTriangle,
    faEye,
    faPlus,
    // @ts-ignore-next-line
} from '@son-video/font-awesome-pro-solid/index.es'
import { PAYMENT_MODES } from '@/shared/referential/customer_order/payment_modes'

/** events */
const emit = defineEmits<{
    (event: 'change'): void
}>()

const columns = ref([
    {
        name: 'description',
    },
    {
        name: 'amount',
        title: 'Montant',
        _columnClasses: 'text-right',
    },
    {
        name: 'status',
        title: 'Statut',
    },
    {
        name: 'actions',
    },
    {
        name: 'details',
        title: 'Détails',
    },
])

const store = useCustomerOrderStore()
const { customer_order, anti_fraud, config, is_loading } = storeToRefs(store)

const payment_v2_store = useCustomerOrderPaymentV2Store()
const {
    customer_order_context,
    is_loading: is_loading_customer_order_context,
    is_worldline_pay_by_link_opened,
} = storeToRefs(payment_v2_store)

const anti_fraud_payments = computed<AntiFraudForCustomerOrderCustomerOrderPayments[]>(
    () => anti_fraud.value?.statuses.anti_fraud_customer_order_payments ?? [],
)
const operations = computed(() => customer_order_context.value?.operations ?? [])

interface ComputedCustomerOrderPayment extends CustomerOrderPayment {
    anti_fraud: null | AntiFraudForCustomerOrderCustomerOrderPayments
    operation: null | PaymentV2OperationDetails
    label: string
}

const payments = computed<ComputedCustomerOrderPayment[]>(() =>
    (customer_order.value?.payments ?? []).map((p) => {
        const payment = {
            ...structuredClone(p),
            anti_fraud: anti_fraud_payments.value.find((af) => af.creation_proof === p.creation_proof) ?? null,
            operation: operations.value.find((o) => o.operation_id === p.operation_id) ?? null,
            label: '',
        }

        if (payment.operation !== null && 'internal_label' in payment.operation.meta) {
            payment.label = setPaymentMode(payment.operation.meta.internal_label)
        }

        return payment
    }),
)

const has_payment_v2 = computed(() => payments.value.some((p) => p.workflow === CustomerOrderPaymentWorkflow.V2))
const computed_columns = computed(() =>
    columns.value.filter((c) => (!has_payment_v2.value ? c.name !== 'actions' : true)),
)
const is_block_displayed = computed(() => is_toggle_forced.value || config.value.payment_block_toggle)
const is_toggle_forced = computed(() => payments.value.some((p) => p.operation?.meta.is_uncertain))

function getAmount(row: CustomerOrderPayment): number {
    if (row.status === CustomerOrderPaymentStatus.CANCELLED) {
        return row.cancellation_amount
    }
    if (row.status === CustomerOrderPaymentStatus.REMITTED) {
        return row.remit_amount
    }
    if (row.status === CustomerOrderPaymentStatus.ACCEPTED) {
        return row.acceptation_amount
    }

    return row.creation_amount
}

function setPaymentMode(internal_label: string): string {
    if (PAYMENT_MODES[internal_label as keyof typeof PAYMENT_MODES] !== undefined) {
        return PAYMENT_MODES[internal_label as keyof typeof PAYMENT_MODES]
    }
    return internal_label
}

const can_add_payment = computed(() => useAuthenticationStore().hasPermission(CUSTOMER_ORDER_PAYMENT_ADD))
const worldline_pay_by_link_props = computed<
    undefined | { customerOrderId: number; customerId: number; customerEmail: string }
>(() =>
    customer_order.value && customer_order_context.value
        ? {
              customerId: customer_order.value.customer_id,
              customerOrderId: customer_order.value.customer_order_id,
              customerEmail: customer_order.value.billing_address.email,
          }
        : undefined,
)

function onActionCompletion() {
    if (!customer_order.value) {
        return
    }
    useCustomerOrderPaymentV2Store().loadByCustomerOrderId(customer_order.value.customer_order_id)
    emit('change')
}

const has_pickup_payment_opened = computed((): boolean => {
    return payments.value.some(
        (payment) =>
            payment.payment_method_id === 70 && // Paiement emport
            payment.cancelled_at === null,
    )
})

function reTypeRow(row: ErpTableRow): row is ComputedCustomerOrderPayment {
    return true
}
</script>

<template>
    <div class="border rounded" data-context="customer-order-payments-block">
        <div class="p-1 border-b">
            <alert
                v-if="!is_block_displayed && has_pickup_payment_opened"
                no-spacing-on-wrapper
                flat
                danger
                class="py-2 px-3 leading-none"
                :use-icon="faExclamationTriangle"
            >
                <span data-context="paiement_emport_open" class="text-red-800 font-medium"
                    >Paiement emport dépot ouvert</span
                >
            </alert>
        </div>

        <erp-table
            v-if="is_block_displayed && payments.length > 0"
            :is-loading="is_loading || is_loading_customer_order_context"
            :presets="[STYLE_PRESETS.spreadsheet, STYLE_PRESETS.compact]"
            :columns="computed_columns"
            :rows="payments"
            no-border
            vertical-align="baseline"
        >
            <template #description="{ row }">
                <div v-if="reTypeRow(row)" class="flex gap-1.5 items-center">
                    <erp-tooltip
                        :message="{
                            content: 'Version du système de paiement',
                            placement: 'bottom-start',
                            arrowPadding: -10,
                        }"
                    >
                        <span class="bg-slate-100 text-slate-700 px-1 py-1 text-[.7rem] leading-none italic">{{
                            row.workflow === CustomerOrderPaymentWorkflow.V2 ? 'V2' : 'V1'
                        }}</span>
                    </erp-tooltip>

                    <erp-tooltip
                        :message="{
                            content: `${row.type === CustomerOrderPaymentType.PAYMENT ? 'Paiement' : 'Remboursement'} - ${row.payment_method_description}`,
                            placement: 'bottom-start',
                            arrowPadding: -10,
                        }"
                        ><span
                            :class="[
                                row.type === CustomerOrderPaymentType.PAYMENT ? 'text-blue-800' : 'text-amber-700',
                            ]"
                            >{{ row.payment_method_code }}</span
                        ></erp-tooltip
                    >

                    <div v-if="row.label.length">
                        <badge>{{ row.label }}</badge>
                    </div>

                    <erp-tooltip
                        v-if="row.auto_warranty"
                        :message="{
                            content: row.auto_warranty_detail,
                            placement: 'bottom-start',
                            arrowPadding: -5,
                        }"
                    >
                        <badge>{{ row.auto_warranty }}</badge>
                    </erp-tooltip>

                    <erp-tooltip
                        v-if="row.card_origin"
                        :message="{
                            content: `Pays d'origine de la carte`,
                            placement: 'bottom-start',
                            arrowPadding: -5,
                        }"
                    >
                        <span class="text-slate-400">{{ row.card_origin }}</span>
                    </erp-tooltip>
                </div>
            </template>

            <template #amount="{ row }">
                <span v-if="reTypeRow(row)" class="font-medium" :class="{ 'text-red-700': 0 > getAmount(row) }">{{
                    formatCurrency(getAmount(row))
                }}</span>
            </template>

            <template #status="{ row }">
                <customer-order-payment-status-badge
                    v-if="reTypeRow(row)"
                    :id="`row-payment-${row.payment_id}`"
                    :payment="row"
                    :operation="row.operation"
                />
            </template>

            <template #actions="{ row }">
                <payment-v2-operation-actions
                    v-if="reTypeRow(row)"
                    mini
                    :operation="row.operation"
                    @completed="onActionCompletion"
                >
                    <erp-tooltip
                        v-if="row.workflow === CustomerOrderPaymentWorkflow.V2 && row.operation_id"
                        :message="{
                            content: `Voir l'historique du paiement`,
                            placement: 'bottom-start',
                            arrowPadding: -10,
                        }"
                    >
                        <erp-button
                            tertiary
                            xs
                            :icon="faEye"
                            :icon-class="['text-blue-600']"
                            data-context="view-details-btn"
                            @click="useCustomerOrderPaymentV2Store().viewDetailsOnOperation(row.operation_id)"
                        />
                    </erp-tooltip>
                </payment-v2-operation-actions>
            </template>

            <template #details="{ row }">
                <div v-if="reTypeRow(row)" class="flex flex-col gap-2">
                    <div
                        v-if="row.payment_method_id === 70 && row.cancelled_at === null"
                        class="flex gap-2 px-2 py-1 border border-red-200 bg-red-50 text-red-800 rounded-sm text-sm font-medium"
                        data-context="paiement_emport_open"
                    >
                        <div class="text-red-600">
                            <font-awesome-icon :icon="faExclamationTriangle" fixed-width class="text-sm" />
                        </div>

                        <span>Paiement emport dépot ouvert</span>
                    </div>

                    <div
                        v-if="
                            row.creation_proof ||
                            (row.accepted_at && row.acceptation_proof) ||
                            (row.remitted_at && row.remit_proof)
                        "
                        class="flex gap-2 flex-wrap"
                    >
                        <erp-tooltip
                            v-if="row.creation_proof"
                            :message="{
                                content: 'Justificatif de création',
                                placement: 'bottom-start',
                                arrowPadding: -10,
                            }"
                        >
                            <span class="bg-slate-100 text-slate-600 px-2 py-1 text-xs italic">{{
                                row.creation_proof
                            }}</span>
                        </erp-tooltip>

                        <erp-tooltip
                            v-if="row.accepted_at && row.acceptation_proof"
                            :message="{
                                content: `Justificatif d'acceptation`,
                                placement: 'bottom-start',
                                arrowPadding: -10,
                            }"
                        >
                            <span class="bg-blue-50 text-blue-800 px-2 py-1 text-xs italic">{{
                                row.acceptation_proof ?? '-'
                            }}</span>
                        </erp-tooltip>

                        <erp-tooltip
                            v-if="row.remitted_at && row.remit_proof"
                            :message="{
                                content: `Justificatif de remise`,
                                placement: 'bottom-start',
                                arrowPadding: -10,
                            }"
                        >
                            <span class="bg-lime-50 text-lime-800 px-2 py-1 text-xs italic">{{
                                row.remit_proof ?? '-'
                            }}</span>
                        </erp-tooltip>
                    </div>

                    <div
                        v-if="row.operation?.meta.is_uncertain"
                        class="flex gap-2 px-2 py-1 border border-red-200 bg-red-50 text-red-800 rounded-sm text-sm font-medium"
                        data-context="paiement_uncertain_warning"
                    >
                        <div class="text-red-600">
                            <font-awesome-icon :icon="faExclamationTriangle" fixed-width class="text-sm" />
                        </div>

                        <span>Le paiement a un statut "Incertain" chez le prestataire.</span>
                    </div>

                    <div
                        v-if="
                            row.anti_fraud &&
                            [AntiFraudStatus.NON_ELIGIBLE, AntiFraudStatus.REJECTED].includes(row.anti_fraud.status)
                        "
                        class="flex gap-2 px-2 py-1 border border-amber-200 bg-amber-50 rounded-sm text-xs"
                    >
                        <div class="text-amber-600">
                            <font-awesome-icon :icon="faExclamationTriangle" fixed-width class="text-sm" />
                        </div>

                        <span v-if="AntiFraudReason.MISSING_3D_SECURE === row.anti_fraud.reason.name"
                            >Le statut 3D Secure est manquant.</span
                        >

                        <erp-tooltip
                            v-if="AntiFraudReason.AUTO_STATUS_IS_NOT_SUPPORTED === row.anti_fraud.reason.name"
                            :message="{
                                content: `Auto statut: ${row.auto_status ?? 'absent'}`,
                                placement: 'bottom',
                            }"
                        >
                            <span>Le statut interne du paiement ne permet pas son acceptation automatique.</span>
                        </erp-tooltip>

                        <span
                            v-if="
                                AntiFraudReason.CREDIT_CARD_ORIGIN_NOT_IN_SCHENGEN_AREA === row.anti_fraud.reason.name
                            "
                            >Le pays d'origine de la carte de paiement ne fait pas partie de l'espace Schengen.</span
                        >

                        <span v-if="AntiFraudReason.PAYMENT_NOT_SUPPORTED === row.anti_fraud.reason.name"
                            >Le moyen de paiement n'est pas éligible à l'acceptation automatique du paiement.</span
                        >
                    </div>
                </div>
            </template>

            <template v-if="customer_order" #footer>
                <tfoot>
                    <tr>
                        <td class="text-right text-slate-600 py-2 border-t border-slate-200" colspan="2">
                            <div class="whitespace-nowrap">
                                <span class="pl-3 text-[.7rem] font-medium text-slate-400 uppercase tracking-wider">
                                    Montant commande :
                                </span>
                                <span class="font-medium text-sm">
                                    {{ formatCurrency(customer_order.total_price_vat_included) }}
                                </span>
                            </div>
                        </td>
                        <td class="text-left pl-5 text-slate-500 py-2 pr-3 border-t border-slate-200" colspan="4">
                            <div class="flex gap-5">
                                <div class="whitespace-nowrap">
                                    <span class="text-[.7rem] font-medium text-slate-400 uppercase tracking-wider">
                                        Montant accepté :
                                    </span>
                                    <span class="font-medium text-sm text-blue-800">
                                        {{ formatCurrency(customer_order.total_accepted_amount) }}
                                    </span>
                                </div>
                                <div class="whitespace-nowrap">
                                    <span class="text-[.7rem] font-medium text-slate-400 uppercase tracking-wider">
                                        Montant remisé :
                                    </span>
                                    <span class="font-medium text-sm text-lime-700">
                                        {{ formatCurrency(customer_order.total_remitted_amount) }}
                                    </span>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tfoot>
            </template>
        </erp-table>

        <div class="flex gap-2 p-2 bg-slate-50">
            <erp-tooltip message="Des informations importantes sont affichées ci-dessus." :disabled="!is_toggle_forced">
                <erp-button
                    :disabled="payments.length === 0 || is_toggle_forced"
                    tertiary
                    data-context="toggle-payment-block"
                    :icon="is_block_displayed ? faAngleDoubleUp : faAngleDoubleDown"
                    :icon-class="['text-blue-500']"
                    @click="config.payment_block_toggle = !config.payment_block_toggle"
                >
                    {{ is_block_displayed ? 'Replier' : 'Déplier' }}
                </erp-button>
            </erp-tooltip>

            <erp-button
                v-if="ONGOING === customer_order?.status"
                :disabled="!can_add_payment"
                tertiary
                data-context="open-add-payment-slide-in"
                :icon="faPlus"
                :icon-class="['text-blue-500']"
                @click="is_worldline_pay_by_link_opened = true"
                >Ajouter un paiement</erp-button
            >
        </div>

        <worldline-add-pay-by-link-payment
            v-if="worldline_pay_by_link_props"
            v-bind="worldline_pay_by_link_props"
            @change="emit('change')"
        />
    </div>
</template>
