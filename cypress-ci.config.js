const { defineConfig } = require('cypress')

module.exports = defineConfig({
    video: false,
    screenshotOnRunFailure: true,
    chromeWebSecurity: false,
    viewportWidth: 1440,
    viewportHeight: 900,
    numTestsKeptInMemory: 0,
    blockHosts: ['*google-analytics.com', '*googletagmanager.com'],
    retries: {
        runMode: 3,
        openMode: 0,
    },
    reporter: 'cypress-multi-reporters',
    reporterOptions: {
        configFile: 'cypress-reporter-config.json',
    },
    e2e: {
        setupNodeEvents(cypress_on, config) {
            const on = require('cypress-on-fix')(cypress_on, config)

            require('cypress-split')(on, config)
            require('cypress-fail-fast/plugin')(on, config)

            return config
        },
        baseUrl: 'http://localhost:8081',
        specPattern: 'tests/e2e/**/*.e2e.js',
        supportFile: 'tests/e2e/support/e2e.js',
        fixturesFolder: 'tests/e2e/fixtures',
        screenshotsFolder: 'tests/e2e/screenshots',
        videosFolder: 'tests/e2e/videos',
    },
})
