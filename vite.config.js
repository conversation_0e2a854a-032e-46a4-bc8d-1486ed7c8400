import { defineConfig } from 'vite'
import vue2 from '@vitejs/plugin-vue2'
import legacy from '@vitejs/plugin-legacy'
import graphql from '@rollup/plugin-graphql'
import { gitDescribeSync } from 'git-describe'
import history from 'connect-history-api-fallback'

const path = require('path')

function dontRewritePath() {
    return {
        name: 'dont-rewrite-php-path',
        configureServer(server) {
            return () => {
                server.middlewares.use(
                    history({
                        // verbose: true, // Uncomment if you need to debug
                        disableDotRule: true,
                        rewrites: [{ from: /.*\.php$/, to: () => '/index.html' }],
                    }),
                )
            }
        },
        configurePreviewServer(server) {
            server.middlewares.use(
                history({
                    // verbose: true, // Uncomment if you need to debug
                    disableDotRule: undefined,
                    rewrites: [{ from: /.*\.php$/, to: '/index.html' }],
                }),
            )
        },
    }
}

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [
        dontRewritePath(), // handle urls using dots in dev and preview, like /legacy/v1/commandes/edition_commande.php
        vue2(),
        legacy({
            targets: ['ie >= 11'],
            additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
        }),
        graphql(),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src'),
            '~@': path.resolve(__dirname, './src'),
            '~bootstrap': 'bootstrap',
            '~@fontsource': '@fontsource',
            '@son-video': '@son-video',
        },
    },
    define: {
        __APP_VERSION__: JSON.stringify(
            process.env.NODE_ENV.includes('dev')
                ? gitDescribeSync(__dirname, {
                      longSemver: false,
                  }).tag
                : process.env.npm_package_version,
        ),
    },
    server: {
        port: 8080,
    },
    test: {
        globals: true,
        threads: false,
        environment: 'jsdom',
        setupFiles: ['./tests/unit/setup.js'],
    },
})
