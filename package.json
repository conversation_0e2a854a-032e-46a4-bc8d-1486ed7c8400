{"name": "erp-client", "version": "1.3.0", "private": true, "scripts": {"serve": "vite", "serve:e2e": "vite --mode e2e --port 8081", "serve:dist": "vite preview --port 8081", "build:preview": "vite build --mode preview", "build:staging": "vite build --mode staging", "build:validation": "vite build --mode validation", "build:production": "vite build --mode production", "build:e2e": "vite build --mode e2e", "build:e2e:serve": "vite build --mode e2e && vite preview --port 8081", "e2e": "start-server-and-test build:e2e:serve http://localhost:8081/ 'cypress run --e2e'", "e2e:open": "start-server-and-test build:e2e:serve http://localhost:8081/ 'cypress open --browser chrome --e2e'", "e2e:open:serve": "start-server-and-test serve:e2e http://localhost:8081/ 'cypress open --browser chrome --e2e'", "e2e:open:standalone": "cypress open --browser chrome --e2e", "component:open": "cypress open --browser chrome --component", "component": "cypress run --browser chrome --component", "component:serve": "start-server-and-test serve:e2e http://localhost:8081/ 'cypress open --browser chrome --component'", "delete:e2e-reports": "rm -rf tests/e2e/results/* || true", "pree2e:ci": "npm run delete:e2e-reports", "e2e:ci": "start-server-and-test serve:dist http://localhost:8081/ 'cypress run --env split=true --browser chrome --e2e --config-file cypress-ci.config.js'", "poste2e:ci": "npm run combine:e2e-reports", "combine:e2e-reports": "jrm tests/e2e/results/combined-report.xml \"tests/e2e/results/*.xml\" && npx mochawesome-merge \"tests/e2e/results/*.json\" > mochawesome.json && npx marge mochawesome.json -o tests/e2e/results/html", "test:unit": "vitest --environment jsdom", "commit": "commit-wizard", "stop-only": "stop-only --folder tests/e2e/specs --folder src/shared/components", "story:dev": "histoire dev", "story:build": "histoire build", "story:preview": "histoire preview"}, "dependencies": {"@floating-ui/dom": "^1.1.0", "@floating-ui/vue": "^0.2.0", "@flowjs/flow.js": "^2.14.0", "@fontsource/open-sans": "4.2.2", "@fontsource/roboto": "4.2.3", "@fortawesome/fontawesome-svg-core": "^1.2.28", "@fortawesome/vue-fontawesome": "^0.1.9", "@panter/vue-i18next": "^0.15.2", "@popperjs/core": "^2.9.2", "@rollup/plugin-graphql": "^2.0.3", "@rushstack/eslint-patch": "^1.3.3", "@son-video/font-awesome-pro-duotone": "^5.13.0", "@son-video/font-awesome-pro-light": "^5.13.0", "@son-video/font-awesome-pro-regular": "^5.13.0", "@son-video/font-awesome-pro-solid": "^5.13.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tiptap/extension-color": "^2.1.13", "@tiptap/extension-highlight": "^2.1.13", "@tiptap/extension-history": "^2.1.13", "@tiptap/extension-image": "^2.1.13", "@tiptap/extension-link": "^2.1.13", "@tiptap/extension-text-style": "^2.1.13", "@tiptap/extension-underline": "^2.1.13", "@tiptap/pm": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "@tiptap/vue-2": "^2.1.13", "@vitejs/plugin-legacy": "^4.0.2", "@vitejs/plugin-vue2": "^2.2.0", "@vue/apollo-composable": "^4.0.2", "@vue/apollo-util": "^4.0.0-beta.6", "@vue/eslint-config-typescript": "^11.0.3", "@vuelidate/core": "^2.0.0", "@vuelidate/validators": "^2.0.0", "@vueuse/core": "^9.11.1", "accounting": "^0.4.1", "apollo-cache-inmemory": "^1.6.6", "apollo-client": "^2.6.10", "apollo-link": "^1.2.14", "apollo-link-context": "^1.0.20", "apollo-link-error": "^1.1.13", "apollo-link-http": "^1.5.17", "apollo-link-retry": "^2.2.16", "autoprefixer": "^10.4.14", "axios": "^1.3.4", "bootstrap": "^4.4.1", "browserslist": "^4.17.2", "connect-history-api-fallback": "^2.0.0", "copy-to-clipboard": "^3.3.1", "crypto-js": "^4.1.1", "date-fns": "^1.29.0", "floating-vue": "^1.0.0-beta.19", "font-awesome": "^4.7.0", "git-describe": "^4.1.1", "graphql": "^15.8.0", "graphql-tag": "^2.12.6", "i18next": "^12.1.0", "js-base64": "^3.7.5", "libphonenumber-js": "^1.10.13", "lodash": "^4.17.21", "markdown-it": "^13.0.1", "pinia": "^2.1.6", "portal-vue": "^2.1.7", "process": "^0.11.10", "qrcode": "^1.4.4", "qs": "^6.10.3", "sass": "^1.61.0", "svg-country-flags": "^1.2.6", "tailwind-children": "^0.5.0", "tailwindcss": "^3.4.1", "tiptap-extensions": "^1.35.2", "tiptap-markdown": "^0.8.7", "uuid": "^3.3.2", "v-click-outside": "^2.1.5", "vite": "^4.2.1", "vue": "^2.7.14", "vue-apollo": "^3.1.2", "vue-axios": "^2.1.5", "vue-gtag": "^1.6.2", "vue-multiselect": "^2.1.6", "vue-observe-visibility": "^0.4.6", "vue-radial-progress": "^0.2.10", "vue-router": "^3.6.5", "vuebar": "0.0.20", "vuedraggable": "^2.24.3", "vuejs-datepicker": "^1.6.2"}, "devDependencies": {"@4tw/cypress-drag-drop": "^2.2.5", "@histoire/plugin-vue2": "^0.17.17", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.14.196", "@types/uuid": "^9.0.8", "@types/vuelidate": "^0.7.21", "@typescript-eslint/parser": "^5.62.0", "@vue/test-utils": "^1.3.0", "@vue/tsconfig": "^0.4.0", "cypress": "^13.9.0", "cypress-commands": "^3.0.0", "cypress-fail-fast": "^7.1.0", "cypress-file-upload": "^5.0.8", "cypress-multi-reporters": "^1.6.4", "cypress-on-fix": "^1.0.3", "cypress-split": "^1.23.2", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "8.10.0", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-vue": "^9.9.0", "histoire": "^0.17.17", "jest-serializer-vue": "^3.1.0", "jsdom": "^20.0.0", "junit-report-merger": "^2.2.2", "mocha": "10.4.0", "mocha-junit-reporter": "^2.0.0", "mochawesome": "^7.1.3", "mochawesome-merge": "^4.2.0", "mochawesome-report-generator": "^6.2.0", "postcss": "^8.4.13", "pre-git": "^3.17.1", "prettier": "3.2.5", "prettier-eslint": "^16.3.0", "shiki": "^0.14.1", "start-server-and-test": "^2.0.3", "stop-only": "3.3.1", "terser": "^5.14.2", "typescript": "^5.1.3", "vitest": "^0.18.1", "vue-composable-tester": "^0.1.3", "vue-docgen-api": "^4.60.0", "vue-eslint-parser": "^8.3.0", "vue-template-compiler": "^2.7.14"}, "browserslist": ["> 1%", "last 2 versions", "iOS >= 9", "Firefox ESR", "Edge >= 13", "not ie <= 10"], "prettier": {"printWidth": 120, "trailingComma": "all", "tabWidth": 4, "semi": false, "singleQuote": true, "arrowParens": "always"}, "release": {"analyzeCommits": "simple-commit-message"}, "config": {"pre-git": {"enabled": true, "pre-commit": ["npm run stop-only"], "allow-untracked-files": true}}}