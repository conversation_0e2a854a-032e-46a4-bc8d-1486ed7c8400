{
    "env": {
        "browser": true,
        "es2021": true,
        "node": true
    },
    "extends": [
        "eslint:recommended",
        "plugin:cypress/recommended",
        "plugin:vue/recommended",
        "prettier",
        "@vue/eslint-config-typescript"
    ],
    "overrides": [],
    "parserOptions": {
        "ecmaVersion": "latest",
        "sourceType": "module"
    },
    "rules": {
        "no-irregular-whitespace": "off",
        "@typescript-eslint/consistent-type-imports": "error" // this help PHPStorm separates modules from types imports
    },
    "settings": {
        "import/resolver": {
            "alias": [["@", "./src"]]
        }
    }
}
