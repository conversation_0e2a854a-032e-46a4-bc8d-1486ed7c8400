describe('Customer order products margin', () => {
    const VISIT_PAGE = (file) => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {},
            },
        }).as('customer_order')

        cy.intercept('POST', '**/api/erp/v1/customer-order-products', { fixture: file }).as(
            'cpost_customer_order_products',
        )

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.emitEvent('open-products-margin', 123456)
        cy.wait('@customer_order')
        cy.wait('@cpost_customer_order_products')
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Displays customer order products margins', () => {
        it('displays margins', () => {
            VISIT_PAGE('erp/customer-order-products/delivered_customer_order.json')

            cy.get('[data-context=slide-out-container] [data-context=products-margins]').as('panel').should('exist')
            cy.get('@panel').find('[data-context=page-header]').should('contain', 'Marges produits')
            cy.get('@panel')
                .find('[data-context=page-header] [data-context=badge]')
                .should('contain', 'Expédiée / Retirée')
        })

        it('displays deliverable status', () => {
            VISIT_PAGE('erp/customer-order-products/deliverable_customer_order.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .find('[data-context=page-header] [data-context=badge]')
                .should('contain', 'Livrable')
        })

        it('displays non-deliverable status', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_availability.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .find('[data-context=page-header] [data-context=badge]')
                .should('contain', 'Non livrable')
        })

        it('displays bookable status on a shipping customer order', () => {
            VISIT_PAGE('erp/customer-order-products/bookable_shipping_customer_order.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .find('[data-context=page-header] [data-context=badge]')
                .should('contain', 'Réservable')

            // product bookable from store
            cy.get('@panel')
                .find('[data-context=table-row]')
                .eq(0)
                .find('[data-context=cell-availability]')
                .should('contain', 'Indisponible entrepôts / Réservable dans un magasin')

            // product bookable from Champigny
            cy.get('@panel')
                .find('[data-context=table-row]')
                .eq(1)
                .find('[data-context=cell-availability]')
                .should('contain', 'Réservable')
        })

        it('displays bookable status on a picking up customer order', () => {
            VISIT_PAGE('erp/customer-order-products/bookable_pickup_customer_order.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .find('[data-context=page-header] [data-context=badge]')
                .should('contain', 'Réservable')

            // product bookable from Champigny
            cy.get('@panel')
                .find('[data-context=table-row]')
                .eq(0)
                .find('[data-context=cell-availability]')
                .should('contain', 'Réservable dans un autre magasin')

            // product bookable from another store
            cy.get('@panel')
                .find('[data-context=table-row]')
                .eq(1)
                .find('[data-context=cell-availability]')
                .should('contain', 'Réservable dans un autre magasin')

            // product bookable from pick up store
            cy.get('@panel')
                .find('[data-context=table-row]')
                .eq(2)
                .find('[data-context=cell-availability]')
                .should('contain', 'Réservable')
        })

        it('does not display product with negative quantity', () => {
            VISIT_PAGE('erp/customer-order-products/delivered_customer_order.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .should('exist')
            // 3 products in customer order with one in negative quantity
            cy.get('@panel').find('[data-context=table-row]').should('have.length', 2)
        })
    })
})
