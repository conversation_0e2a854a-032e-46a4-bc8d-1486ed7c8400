const defaultTheme = require('tailwindcss/defaultTheme')
const plugin = require('tailwindcss/plugin')

const round = (num) =>
    num
        .toFixed(7)
        .replace(/(\.[0-9]+?)0+$/, '$1')
        .replace(/\.0$/, '')
const rem = (px) => `${round(px / 16)}rem`
const em = (px, base) => `${round(px / base)}em`

module.exports = {
    darkMode: 'class',
    content: ['./public/**/*.html', './src/**/*.{vue,js,ts}'],
    theme: {
        extend: {
            colors: {
                shark: {
                    50: '#e7e7e9',
                    100: '#d0d4d8',
                    200: '#b0b7bf',
                    300: '#8a949e',
                    400: '#677079',
                    500: '#4f565f',
                    600: '#3c4248',
                    700: '#2f3237',
                    800: '#26282c',
                    900: '#1f2123',
                    950: '#101113',
                },
                sidebar: {
                    100: 'hsl(220, 5%, 68%)',
                    300: 'hsl(220, 9%, 32%)',
                    400: 'hsl(220, 9%, 20%)',
                    500: 'hsl(225, 9%, 15%)',
                    600: 'hsl(220, 7%, 11%)',
                    700: 'hsl(220, 5%, 8%)',
                },
                svd: {
                    50: '#EBF0FA',
                    100: '#D6E0F5',
                    200: '#ADC2EB',
                    300: '#85A3E0',
                    400: '#5C85D6',
                    500: '#3366CC',
                    600: '#2952A3',
                    700: '#1F3D7A',
                    800: '#142952',
                    900: '#0A1429',
                },
                ezl: '#c9040d',
            },
            flex: {
                '0-auto': '0 0 auto',
            },
            inset: {
                '50px': '50px',
            },
            fontFamily: {
                sans: ['Roboto', ...defaultTheme.fontFamily.sans],
                web: ['"Open Sans"', 'Roboto', ...defaultTheme.fontFamily.sans],
            },
            fontSize: {
                '2xs': '.65rem',
            },
            maxWidth: {
                250: '250px',
                280: '280px',
                380: '380px',
                768: '768px',
                960: '960px',
                1205: '1205px',
                1440: '1440px',
            },
            minWidth: {
                14: '3.5rem',
                16: '4rem',
                24: '6rem',
                250: '250px',
                280: '280px',
                380: '380px',
                960: '960px',
                1172: '1172px',
                1440: '1440px',
            },
            minHeight: {
                36: '9rem',
            },
            padding: {
                'sidebar-open': '230px',
                'sidebar-partially-collapsed': '65px',
            },
            width: {
                'sidebar-open': '230px',
                'sidebar-partially-collapsed': '65px',
                250: '250px',
                280: '280px',
                380: '380px',
                768: '768px',
                1172: '1172px',
            },
            keyframes: {
                skeletonAnimation: {
                    '0%': { transform: 'translate3d(-60%, 0, 0)' },
                    '100%': { transform: 'translate3d(100%, 0, 0)' },
                },
            },
            animation: {
                skeleton: 'skeletonAnimation 1.5s linear infinite forwards',
            },
            typography: (theme) => ({
                sm: {
                    css: {
                        '--prose-red-text-color': theme('colors.red.600'),
                        '--prose-yellow-text-color': theme('colors.yellow.700'),
                        '--prose-green-text-color': theme('colors.green.600'),
                        '--prose-blue-text-color': theme('colors.blue.600'),
                        color: theme('currentColor'),
                        a: {
                            color: '#466EDC',
                            '&:hover': {
                                color: '#2750A6',
                            },
                        },
                        code: {
                            color: theme('colors.pink.600'),
                            fontWeight: '400',
                        },
                        'code::before': {
                            content: '',
                        },
                        'code::after': {
                            content: '',
                        },
                        p: {
                            marginTop: theme('spacing.3', defaultTheme.spacing['3']),
                            marginBottom: 0,
                        },
                        h1: {
                            fontSize: theme('fontSize.base.2xl', defaultTheme.fontSize['2xl'][0]),
                            lineHeight: theme(
                                'fontSize.base.2xl.lineHeight',
                                defaultTheme.fontSize['2xl'][1].lineHeight,
                            ),
                            marginTop: theme('spacing.3', defaultTheme.spacing['3']),
                            marginBottom: theme('spacing.3', defaultTheme.spacing['3']),
                        },
                        h2: {
                            fontSize: theme('fontSize.base.xl', defaultTheme.fontSize.xl[0]),
                            lineHeight: theme('fontSize.base.xl.lineHeight', defaultTheme.fontSize.xl[1].lineHeight),
                            marginTop: theme('spacing.3', defaultTheme.spacing['3']),
                            marginBottom: theme('spacing.3', defaultTheme.spacing['3']),
                        },
                        h3: {
                            fontSize: theme('fontSize.base.lg', defaultTheme.fontSize.lg[0]),
                            lineHeight: theme('fontSize.base.lg.lineHeight', defaultTheme.fontSize.lg[1].lineHeight),
                            marginTop: theme('spacing.3', defaultTheme.spacing['3']),
                            marginBottom: theme('spacing.3', defaultTheme.spacing['3']),
                        },
                        h4: {
                            fontSize: theme('fontSize.base.lg', defaultTheme.fontSize.lg[0]),
                            lineHeight: theme('fontSize.base.lg.lineHeight', defaultTheme.fontSize.lg[1].lineHeight),
                            marginTop: theme('spacing.3', defaultTheme.spacing['3']),
                            marginBottom: theme('spacing.3', defaultTheme.spacing['3']),
                        },
                        h5: {
                            fontSize: theme('fontSize.base.lg', defaultTheme.fontSize.lg[0]),
                            lineHeight: theme('fontSize.base.lg.lineHeight', defaultTheme.fontSize.lg[1].lineHeight),
                            marginTop: theme('spacing.3', defaultTheme.spacing['3']),
                            marginBottom: theme('spacing.3', defaultTheme.spacing['3']),
                        },
                        h6: {
                            fontSize: theme('fontSize.base.lg', defaultTheme.fontSize.lg[0]),
                            lineHeight: theme('fontSize.base.lg.lineHeight', defaultTheme.fontSize.lg[1].lineHeight),
                            marginTop: theme('spacing.3', defaultTheme.spacing['3']),
                            marginBottom: theme('spacing.3', defaultTheme.spacing['3']),
                        },
                        hr: {
                            marginTop: theme('spacing.3', defaultTheme.spacing['3']),
                            marginBottom: theme('spacing.3', defaultTheme.spacing['3']),
                        },
                        '> ul > li > *:first-child': {
                            marginTop: em(10, 14),
                        },
                        '> ol > li > *:first-child': {
                            marginTop: em(10, 14),
                        },
                        mark: {
                            padding: 'unset',
                            backgroundColor: 'mark',
                        },
                    },
                },
            }),
        },
    },
    plugins: [
        require('@tailwindcss/forms'),
        require('@tailwindcss/typography'),
        require('@tailwindcss/aspect-ratio'),
        require('tailwind-children'),
        plugin(function ({ addUtilities, addVariant }) {
            addUtilities({
                '.backface-visible': {
                    'backface-visibility': 'visible',
                },
                '.backface-hidden': {
                    'backface-visibility': 'hidden',
                },
            })
            addVariant('not-last', '&:not(:last-child)')
            addVariant('not-last-td', '&:not(:last-child) > td')
        }),
    ],
}
