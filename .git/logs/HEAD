1b4a13569cb3ff1c43d8bf1aa729ab6ddb36f0a0 bf01cc441e9d5d0297b1ec3e1b942d559f1e9ead RomainM <<EMAIL>> 1735922906 +0100	checkout: moving from validation to master
bf01cc441e9d5d0297b1ec3e1b942d559f1e9ead d518c2dfca46ec8b629befc2f27f47fac0d446bf RomainM <<EMAIL>> 1735922915 +0100	pull origin: Fast-forward
d518c2dfca46ec8b629befc2f27f47fac0d446bf 3bf8ebc7e6c607cc259b320c6be0cff15cb4f88a RomainM <<EMAIL>> 1736329419 +0100	rebase (start): checkout origin/master
3bf8ebc7e6c607cc259b320c6be0cff15cb4f88a 3bf8ebc7e6c607cc259b320c6be0cff15cb4f88a RomainM <<EMAIL>> 1736329419 +0100	rebase (finish): returning to refs/heads/master
3bf8ebc7e6c607cc259b320c6be0cff15cb4f88a 7542de4ae56fcc99487236ceb98f9ebe9fe92533 RomainM <<EMAIL>> 1736329424 +0100	checkout: moving from master to validation
7542de4ae56fcc99487236ceb98f9ebe9fe92533 1a012e61b275c025fbd1521812552205c7fb67c3 RomainM <<EMAIL>> 1736329435 +0100	merge refs/heads/pricing_strategy/release: Merge made by the 'ort' strategy.
1a012e61b275c025fbd1521812552205c7fb67c3 ad1c53c0e4bfad7ef3b4500ff79ab173153186fc RomainM <<EMAIL>> 1736350234 +0100	checkout: moving from validation to pricing_strategy/release
ad1c53c0e4bfad7ef3b4500ff79ab173153186fc a4d9c9a095c523636c21187f79ca505a8bed1052 RomainM <<EMAIL>> 1736350259 +0100	commit: feat[support#2218]: add competitors
a4d9c9a095c523636c21187f79ca505a8bed1052 1a012e61b275c025fbd1521812552205c7fb67c3 RomainM <<EMAIL>> 1736350680 +0100	checkout: moving from pricing_strategy/release to validation
1a012e61b275c025fbd1521812552205c7fb67c3 c769c970a9e039e309fcd3619d7e682dd926cb52 RomainM <<EMAIL>> 1736350696 +0100	merge refs/heads/pricing_strategy/release: Merge made by the 'ort' strategy.
c769c970a9e039e309fcd3619d7e682dd926cb52 716d8404ec37846e07e84ac949d02962428ce2fa RomainM <<EMAIL>> 1736350723 +0100	checkout: moving from validation to multi-channel-pricing/v1/release
716d8404ec37846e07e84ac949d02962428ce2fa a4d9c9a095c523636c21187f79ca505a8bed1052 RomainM <<EMAIL>> 1736351211 +0100	checkout: moving from multi-channel-pricing/v1/release to pricing_strategy/release
a4d9c9a095c523636c21187f79ca505a8bed1052 716d8404ec37846e07e84ac949d02962428ce2fa RomainM <<EMAIL>> 1736351285 +0100	checkout: moving from pricing_strategy/release to multi-channel-pricing/v1/release
716d8404ec37846e07e84ac949d02962428ce2fa a4d9c9a095c523636c21187f79ca505a8bed1052 RomainM <<EMAIL>> 1736351335 +0100	checkout: moving from multi-channel-pricing/v1/release to pricing_strategy/release
a4d9c9a095c523636c21187f79ca505a8bed1052 716d8404ec37846e07e84ac949d02962428ce2fa RomainM <<EMAIL>> 1736351356 +0100	checkout: moving from pricing_strategy/release to multi-channel-pricing/v1/release
716d8404ec37846e07e84ac949d02962428ce2fa a4d9c9a095c523636c21187f79ca505a8bed1052 RomainM <<EMAIL>> 1736351715 +0100	checkout: moving from multi-channel-pricing/v1/release to pricing_strategy/release
a4d9c9a095c523636c21187f79ca505a8bed1052 c769c970a9e039e309fcd3619d7e682dd926cb52 RomainM <<EMAIL>> 1736352260 +0100	checkout: moving from pricing_strategy/release to validation
c769c970a9e039e309fcd3619d7e682dd926cb52 bdf55efb91e7361ef8e9e23d6b99095154da5c26 RomainM <<EMAIL>> 1736352264 +0100	merge refs/heads/multi-channel-pricing/v1/release: Merge made by the 'ort' strategy.
bdf55efb91e7361ef8e9e23d6b99095154da5c26 716d8404ec37846e07e84ac949d02962428ce2fa RomainM <<EMAIL>> 1736352530 +0100	checkout: moving from validation to multi-channel-pricing/v1/release
716d8404ec37846e07e84ac949d02962428ce2fa a4d9c9a095c523636c21187f79ca505a8bed1052 RomainM <<EMAIL>> 1736427135 +0100	checkout: moving from multi-channel-pricing/v1/release to pricing_strategy/release
a4d9c9a095c523636c21187f79ca505a8bed1052 4c520c025511dd050e234aaff923d769f4308178 RomainM <<EMAIL>> 1736852991 +0100	checkout: moving from pricing_strategy/release to master
4c520c025511dd050e234aaff923d769f4308178 4c520c025511dd050e234aaff923d769f4308178 RomainM <<EMAIL>> 1736853003 +0100	checkout: moving from master to chatbase
4c520c025511dd050e234aaff923d769f4308178 67ab4064098605b40d70296c50223ce6ba63e219 RomainM <<EMAIL>> 1736853162 +0100	commit: add chatbase
67ab4064098605b40d70296c50223ce6ba63e219 24d839b99eba0c8f5dda603123e204a021204824 RomainM <<EMAIL>> 1736853185 +0100	checkout: moving from chatbase to validation
24d839b99eba0c8f5dda603123e204a021204824 403f842ee417a22c5a92b60500357becb634b639 RomainM <<EMAIL>> 1736853192 +0100	merge refs/heads/chatbase: Merge made by the 'ort' strategy.
403f842ee417a22c5a92b60500357becb634b639 4c520c025511dd050e234aaff923d769f4308178 RomainM <<EMAIL>> 1737018950 +0100	checkout: moving from validation to master
4c520c025511dd050e234aaff923d769f4308178 ce080821e16773c6340b7cd51e9818079a325961 RomainM <<EMAIL>> 1737018960 +0100	pull origin: Fast-forward
ce080821e16773c6340b7cd51e9818079a325961 a4d9c9a095c523636c21187f79ca505a8bed1052 RomainM <<EMAIL>> 1737468644 +0100	checkout: moving from master to pricing_strategy/release
a4d9c9a095c523636c21187f79ca505a8bed1052 9c8150e4488a783b004be987bdff1bef9aac3d81 RomainM <<EMAIL>> 1737468663 +0100	checkout: moving from pricing_strategy/release to master
9c8150e4488a783b004be987bdff1bef9aac3d81 9c8150e4488a783b004be987bdff1bef9aac3d81 RomainM <<EMAIL>> 1737469008 +0100	checkout: moving from master to pricing_strategy/fix_competitors
9c8150e4488a783b004be987bdff1bef9aac3d81 ceca5b16f938efcd0bdcb9beda62c0a08341e04a RomainM <<EMAIL>> 1737469081 +0100	commit: fix pricing strategies competitors
ceca5b16f938efcd0bdcb9beda62c0a08341e04a 9c8150e4488a783b004be987bdff1bef9aac3d81 RomainM <<EMAIL>> 1737477409 +0100	checkout: moving from pricing_strategy/fix_competitors to master
9c8150e4488a783b004be987bdff1bef9aac3d81 6035479dbbd08a96586d80b585f43db18fe6fe02 RomainM <<EMAIL>> 1737477420 +0100	rebase (start): checkout origin/master
6035479dbbd08a96586d80b585f43db18fe6fe02 6035479dbbd08a96586d80b585f43db18fe6fe02 RomainM <<EMAIL>> 1737477420 +0100	rebase (finish): returning to refs/heads/master
6035479dbbd08a96586d80b585f43db18fe6fe02 403f842ee417a22c5a92b60500357becb634b639 RomainM <<EMAIL>> 1737970739 +0100	checkout: moving from master to validation
403f842ee417a22c5a92b60500357becb634b639 f52866e76ccaac5fc57f5529dabc0287882f759c RomainM <<EMAIL>> 1737970747 +0100	rebase (start): checkout origin/validation
f52866e76ccaac5fc57f5529dabc0287882f759c f52866e76ccaac5fc57f5529dabc0287882f759c RomainM <<EMAIL>> 1737970748 +0100	rebase (finish): returning to refs/heads/validation
f52866e76ccaac5fc57f5529dabc0287882f759c 85077af33a2f921b891d73acd0a584b6e3c59ae3 RomainM <<EMAIL>> 1737970886 +0100	merge refs/remotes/origin/multi-channel-pricing/promo-code-support: Merge made by the 'ort' strategy.
85077af33a2f921b891d73acd0a584b6e3c59ae3 6035479dbbd08a96586d80b585f43db18fe6fe02 RomainM <<EMAIL>> 1737987407 +0100	checkout: moving from validation to master
6035479dbbd08a96586d80b585f43db18fe6fe02 133857a415a9bd7d2612083c952a7b26baae451c RomainM <<EMAIL>> 1737987414 +0100	rebase (start): checkout origin/master
133857a415a9bd7d2612083c952a7b26baae451c 133857a415a9bd7d2612083c952a7b26baae451c RomainM <<EMAIL>> 1737987414 +0100	rebase (finish): returning to refs/heads/master
133857a415a9bd7d2612083c952a7b26baae451c 1158d66640d2d2228f0ce21d9e17079c9f44d002 RomainM <<EMAIL>> 1738593069 +0100	pull origin: Fast-forward
1158d66640d2d2228f0ce21d9e17079c9f44d002 1158d66640d2d2228f0ce21d9e17079c9f44d002 RomainM <<EMAIL>> 1738593092 +0100	checkout: moving from master to pricing_strategy/conflits
1158d66640d2d2228f0ce21d9e17079c9f44d002 d3cf035ed94c4f532b1e7515712737e0ac0d2680 RomainM <<EMAIL>> 1738593103 +0100	commit: WIP conflits
d3cf035ed94c4f532b1e7515712737e0ac0d2680 1158d66640d2d2228f0ce21d9e17079c9f44d002 RomainM <<EMAIL>> 1738593107 +0100	checkout: moving from pricing_strategy/conflits to master
1158d66640d2d2228f0ce21d9e17079c9f44d002 1158d66640d2d2228f0ce21d9e17079c9f44d002 RomainM <<EMAIL>> 1738685090 +0100	checkout: moving from master to support_2322
1158d66640d2d2228f0ce21d9e17079c9f44d002 1579331ba8f3339358404b0a21765c66a270ae81 RomainM <<EMAIL>> 1738685180 +0100	commit: feat[support#2322]:
1579331ba8f3339358404b0a21765c66a270ae81 638aa20c0c0549becd1ea7c4d641338da7a04fba RomainM <<EMAIL>> 1740394331 +0100	checkout: moving from support_2322 to validation
638aa20c0c0549becd1ea7c4d641338da7a04fba 1158d66640d2d2228f0ce21d9e17079c9f44d002 RomainM <<EMAIL>> 1740394385 +0100	checkout: moving from validation to master
1158d66640d2d2228f0ce21d9e17079c9f44d002 0e650cc09dbd5c435bc692a22917730acd4516fb RomainM <<EMAIL>> 1740394391 +0100	rebase (start): checkout origin/master
0e650cc09dbd5c435bc692a22917730acd4516fb 0e650cc09dbd5c435bc692a22917730acd4516fb RomainM <<EMAIL>> 1740394392 +0100	rebase (finish): returning to refs/heads/master
0e650cc09dbd5c435bc692a22917730acd4516fb 638aa20c0c0549becd1ea7c4d641338da7a04fba RomainM <<EMAIL>> 1740394513 +0100	checkout: moving from master to validation
638aa20c0c0549becd1ea7c4d641338da7a04fba 056f5083f85ad45960f6d0a7e463678bc08c10fc RomainM <<EMAIL>> 1740395221 +0100	checkout: moving from validation to pricing_strategy/release
056f5083f85ad45960f6d0a7e463678bc08c10fc 4e3da24e23760ade7ed407e81b161229e5e435ee RomainM <<EMAIL>> 1740395279 +0100	commit: feat[support]: remove unused button
4e3da24e23760ade7ed407e81b161229e5e435ee 638aa20c0c0549becd1ea7c4d641338da7a04fba RomainM <<EMAIL>> 1740395338 +0100	checkout: moving from pricing_strategy/release to validation
638aa20c0c0549becd1ea7c4d641338da7a04fba ed673d1f8c88f83471afdbe4975807f4bb4bc64a RomainM <<EMAIL>> 1740395352 +0100	merge refs/heads/pricing_strategy/release: Merge made by the 'ort' strategy.
ed673d1f8c88f83471afdbe4975807f4bb4bc64a 7e27868e0778e72839329bbff053cf8184fedc8f RomainM <<EMAIL>> 1740411255 +0100	rebase (start): checkout origin/validation
7e27868e0778e72839329bbff053cf8184fedc8f 7e27868e0778e72839329bbff053cf8184fedc8f RomainM <<EMAIL>> 1740411255 +0100	rebase (finish): returning to refs/heads/validation
7e27868e0778e72839329bbff053cf8184fedc8f 953bb4e6e7ec555e5e1a25af98ea85bfe01b70d5 RomainM <<EMAIL>> 1740471080 +0100	checkout: moving from validation to pricing_strategy/release
953bb4e6e7ec555e5e1a25af98ea85bfe01b70d5 03c652d2bd8037c0b036d2a5fdad7abd6d014055 RomainM <<EMAIL>> 1740471095 +0100	commit: feat[support]: fix low margin tab
03c652d2bd8037c0b036d2a5fdad7abd6d014055 7e27868e0778e72839329bbff053cf8184fedc8f RomainM <<EMAIL>> 1740471108 +0100	checkout: moving from pricing_strategy/release to validation
4d5e88ec7359848481e64f6017177bd29aef2f47 d0732447d03a9406f40c892947550da2613e1bcd RomainM <<EMAIL>> 1740471140 +0100	rebase (pick): feat[support]: fix low margin tab
d0732447d03a9406f40c892947550da2613e1bcd d0732447d03a9406f40c892947550da2613e1bcd RomainM <<EMAIL>> 1740471140 +0100	rebase (finish): returning to refs/heads/validation
d0732447d03a9406f40c892947550da2613e1bcd 03c652d2bd8037c0b036d2a5fdad7abd6d014055 RomainM <<EMAIL>> 1740474773 +0100	checkout: moving from validation to pricing_strategy/release
03c652d2bd8037c0b036d2a5fdad7abd6d014055 6ebbd9222496d44d217b881087843eccce303257 RomainM <<EMAIL>> 1740475395 +0100	commit: feat[support]: fix dashboard request params
6ebbd9222496d44d217b881087843eccce303257 d0732447d03a9406f40c892947550da2613e1bcd RomainM <<EMAIL>> 1740475730 +0100	checkout: moving from pricing_strategy/release to validation
d0732447d03a9406f40c892947550da2613e1bcd ea11b5393b84eb1a90cff369170331e9b9df5dde RomainM <<EMAIL>> 1740475737 +0100	merge refs/heads/pricing_strategy/release: Merge made by the 'ort' strategy.
ea11b5393b84eb1a90cff369170331e9b9df5dde 6ebbd9222496d44d217b881087843eccce303257 RomainM <<EMAIL>> 1740482313 +0100	checkout: moving from validation to pricing_strategy/release
6ebbd9222496d44d217b881087843eccce303257 6ebbd9222496d44d217b881087843eccce303257 RomainM <<EMAIL>> 1740490042 +0100	checkout: moving from pricing_strategy/release to pricing_strategy/2351
6ebbd9222496d44d217b881087843eccce303257 db6e5a86f315f3b1b3f8fee787854a55c803b1e8 RomainM <<EMAIL>> 1740501433 +0100	commit: feat[support#2351]: change source for competitors pricing
db6e5a86f315f3b1b3f8fee787854a55c803b1e8 408c6fe1c524f223c00014fa5c2110377217ca5e RomainM <<EMAIL>> 1740502512 +0100	commit: feat[support#2351]: remove filters for strategies dashboard
408c6fe1c524f223c00014fa5c2110377217ca5e 6ebbd9222496d44d217b881087843eccce303257 RomainM <<EMAIL>> 1740996481 +0100	checkout: moving from pricing_strategy/2351 to pricing_strategy/release
6ebbd9222496d44d217b881087843eccce303257 4f8f4b29aa30720d67afec334493573225276c71 RomainM <<EMAIL>> 1740996491 +0100	rebase (start): checkout origin/pricing_strategy/release
4f8f4b29aa30720d67afec334493573225276c71 4f8f4b29aa30720d67afec334493573225276c71 RomainM <<EMAIL>> 1740996491 +0100	rebase (finish): returning to refs/heads/pricing_strategy/release
4f8f4b29aa30720d67afec334493573225276c71 4f8f4b29aa30720d67afec334493573225276c71 RomainM <<EMAIL>> 1741013555 +0100	checkout: moving from pricing_strategy/release to pricing_strategy/2355
4f8f4b29aa30720d67afec334493573225276c71 ba5435b38b378b600342f4532a7746d94fa1c8b2 RomainM <<EMAIL>> 1741084959 +0100	commit: feat[support#2355]: fix columns ordering
ba5435b38b378b600342f4532a7746d94fa1c8b2 f36eb7912a385596893c7e0111f9b2c56da1696c RomainM <<EMAIL>> 1741085153 +0100	commit: feat[support#2355]: remove info button from preview tab
f36eb7912a385596893c7e0111f9b2c56da1696c 7141c0920d10a42c8f2d511e26638bf0360b3e66 RomainM <<EMAIL>> 1741100673 +0100	commit (merge): Merge branch 'refs/heads/pricing_strategy/release' into pricing_strategy/2355
7141c0920d10a42c8f2d511e26638bf0360b3e66 cc225c90d618f635d75160e75cbd85bd079bd5b1 RomainM <<EMAIL>> 1741100758 +0100	commit: feat[support#2355]: fix tests
cc225c90d618f635d75160e75cbd85bd079bd5b1 2e8b81bf17cac907f770498d2be825c4ab941bc4 RomainM <<EMAIL>> 1741103449 +0100	checkout: moving from pricing_strategy/2355 to validation
2e8b81bf17cac907f770498d2be825c4ab941bc4 34f2948ec72eec79f6f90ddb8d187b53f6d3e4bd RomainM <<EMAIL>> 1741103464 +0100	checkout: moving from validation to pricing_strategy/release
34f2948ec72eec79f6f90ddb8d187b53f6d3e4bd 2e8b81bf17cac907f770498d2be825c4ab941bc4 RomainM <<EMAIL>> 1741103475 +0100	checkout: moving from pricing_strategy/release to validation
2e8b81bf17cac907f770498d2be825c4ab941bc4 b511739f0e0966936511efa48cfc0773a945bfcb RomainM <<EMAIL>> 1741103483 +0100	merge refs/heads/pricing_strategy/release: Merge made by the 'ort' strategy.
b511739f0e0966936511efa48cfc0773a945bfcb 34f2948ec72eec79f6f90ddb8d187b53f6d3e4bd RomainM <<EMAIL>> 1741103948 +0100	checkout: moving from validation to pricing_strategy/release
34f2948ec72eec79f6f90ddb8d187b53f6d3e4bd 95c06e097209afbcec9b666dce53d86a2fad1e7f RomainM <<EMAIL>> 1741104615 +0100	commit: feat[support]: fix display
95c06e097209afbcec9b666dce53d86a2fad1e7f b511739f0e0966936511efa48cfc0773a945bfcb RomainM <<EMAIL>> 1741104629 +0100	checkout: moving from pricing_strategy/release to validation
b511739f0e0966936511efa48cfc0773a945bfcb 941bae4f2eb720bff3fc3450e011882c65756cea RomainM <<EMAIL>> 1741104635 +0100	merge refs/heads/pricing_strategy/release: Merge made by the 'ort' strategy.
941bae4f2eb720bff3fc3450e011882c65756cea 0e650cc09dbd5c435bc692a22917730acd4516fb RomainM <<EMAIL>> 1741264528 +0100	checkout: moving from validation to master
0e650cc09dbd5c435bc692a22917730acd4516fb 787b9967096b8d90015e4ef564b621a58b03bd35 RomainM <<EMAIL>> 1741264536 +0100	rebase (start): checkout origin/master
787b9967096b8d90015e4ef564b621a58b03bd35 787b9967096b8d90015e4ef564b621a58b03bd35 RomainM <<EMAIL>> 1741264537 +0100	rebase (finish): returning to refs/heads/master
787b9967096b8d90015e4ef564b621a58b03bd35 6904c09b09a5e81a20f370e026752cce7cb739a0 RomainM <<EMAIL>> 1741264554 +0100	checkout: moving from master to 6904c09b09a5e81a20f370e026752cce7cb739a0
6904c09b09a5e81a20f370e026752cce7cb739a0 ad1c53c0e4bfad7ef3b4500ff79ab173153186fc RomainM <<EMAIL>> 1741264600 +0100	checkout: moving from 6904c09b09a5e81a20f370e026752cce7cb739a0 to ad1c53c0e4bfad7ef3b4500ff79ab173153186fc
ad1c53c0e4bfad7ef3b4500ff79ab173153186fc 787b9967096b8d90015e4ef564b621a58b03bd35 RomainM <<EMAIL>> 1741264667 +0100	checkout: moving from ad1c53c0e4bfad7ef3b4500ff79ab173153186fc to master
787b9967096b8d90015e4ef564b621a58b03bd35 276bec5e6a284703ee1a45ca763c690afd473fcf RomainM <<EMAIL>> 1741277264 +0100	checkout: moving from master to validation
276bec5e6a284703ee1a45ca763c690afd473fcf 95c06e097209afbcec9b666dce53d86a2fad1e7f RomainM <<EMAIL>> 1741614065 +0100	checkout: moving from validation to pricing_strategy/release
95c06e097209afbcec9b666dce53d86a2fad1e7f f84600ab68c38ea8dc4291fabefccbd5c0f4efca RomainM <<EMAIL>> 1741614113 +0100	commit (merge): Merge branch 'refs/heads/master' into pricing_strategy/release
f84600ab68c38ea8dc4291fabefccbd5c0f4efca 08f4de9da049e2bbdd951e857f52238d04c49f61 RomainM <<EMAIL>> 1741624774 +0100	checkout: moving from pricing_strategy/release to master
08f4de9da049e2bbdd951e857f52238d04c49f61 d073bad587879bbdaecf09f9e6acb35bbc99fa47 RomainM <<EMAIL>> 1741624783 +0100	rebase (start): checkout origin/master
d073bad587879bbdaecf09f9e6acb35bbc99fa47 d073bad587879bbdaecf09f9e6acb35bbc99fa47 RomainM <<EMAIL>> 1741624783 +0100	rebase (finish): returning to refs/heads/master
d073bad587879bbdaecf09f9e6acb35bbc99fa47 5455bba575988be57912cd42a4dd30041a690faa RomainM <<EMAIL>> 1741625029 +0100	commit: hotfix: merge problem
5455bba575988be57912cd42a4dd30041a690faa a10620da24f2c572102569ce7efa72206125e697 RomainM <<EMAIL>> 1741713089 +0100	rebase (start): checkout origin/master
a10620da24f2c572102569ce7efa72206125e697 a10620da24f2c572102569ce7efa72206125e697 RomainM <<EMAIL>> 1741713089 +0100	rebase (finish): returning to refs/heads/master
a10620da24f2c572102569ce7efa72206125e697 a10620da24f2c572102569ce7efa72206125e697 RomainM <<EMAIL>> 1741713603 +0100	checkout: moving from master to pricing_strategy/fix_article_search_display
a10620da24f2c572102569ce7efa72206125e697 cfb2682760d2f5abe4c60bcabf83f9e8e40b6f0c RomainM <<EMAIL>> 1741767324 +0100	commit: fix pricing_strategy link to article page
cfb2682760d2f5abe4c60bcabf83f9e8e40b6f0c 276bec5e6a284703ee1a45ca763c690afd473fcf RomainM <<EMAIL>> 1741784359 +0100	checkout: moving from pricing_strategy/fix_article_search_display to validation
276bec5e6a284703ee1a45ca763c690afd473fcf 0c7985379344cf05924c488a26adabd32adf4f7b RomainM <<EMAIL>> 1741784363 +0100	merge refs/heads/pricing_strategy/fix_article_search_display: Merge made by the 'ort' strategy.
0c7985379344cf05924c488a26adabd32adf4f7b a10620da24f2c572102569ce7efa72206125e697 RomainM <<EMAIL>> 1741862926 +0100	checkout: moving from validation to master
a10620da24f2c572102569ce7efa72206125e697 f615316856b33fd941bbdf8e32808907238a018b RomainM <<EMAIL>> 1741862939 +0100	rebase (start): checkout origin/master
f615316856b33fd941bbdf8e32808907238a018b f615316856b33fd941bbdf8e32808907238a018b RomainM <<EMAIL>> 1741862940 +0100	rebase (finish): returning to refs/heads/master
f615316856b33fd941bbdf8e32808907238a018b b2cc2f431ef91345a846c8d80cb83f4b742a2cdb RomainM <<EMAIL>> 1741863205 +0100	checkout: moving from master to pricing_strategy/2367_nice_to_have
b2cc2f431ef91345a846c8d80cb83f4b742a2cdb f1619649c9caf86e24d3e000335b759c81206ec1 RomainM <<EMAIL>> 1741864035 +0100	commit: feat[support#2367]: change competitor pricing order
f1619649c9caf86e24d3e000335b759c81206ec1 02cd1d5ff2cb3e7d604935464e2ef7e9d2e2af92 RomainM <<EMAIL>> 1741882763 +0100	commit: feat[support#2367]: add tooltip to show margin
02cd1d5ff2cb3e7d604935464e2ef7e9d2e2af92 4f68fcbb98fe3e766116a06b704f4f31c15cebfa RomainM <<EMAIL>> 1741940457 +0100	commit: feat[support#2367]: fix label in test
4f68fcbb98fe3e766116a06b704f4f31c15cebfa 2b22c0fad2457bcdfd489d72181529201abb2e3d RomainM <<EMAIL>> 1741965009 +0100	commit: feat[support#2367]: disabled price edition if article under strategy + link to strategy + tests
2b22c0fad2457bcdfd489d72181529201abb2e3d 88275436983e8139d3f9dd7d5d97bfb17074b484 RomainM <<EMAIL>> 1742209063 +0100	checkout: moving from pricing_strategy/2367_nice_to_have to pricing_strategy/release_2
88275436983e8139d3f9dd7d5d97bfb17074b484 47940d1a8742e098181f72712cf961fb2c6ebbd8 RomainM <<EMAIL>> 1742209114 +0100	commit (merge): Merge branch 'refs/heads/support_2322' into pricing_strategy/release_2
47940d1a8742e098181f72712cf961fb2c6ebbd8 b00a3698efa192b858af39552c9c0c71fdfc3755 RomainM <<EMAIL>> 1742308492 +0100	checkout: moving from pricing_strategy/release_2 to master
b00a3698efa192b858af39552c9c0c71fdfc3755 2b22c0fad2457bcdfd489d72181529201abb2e3d RomainM <<EMAIL>> 1742310112 +0100	checkout: moving from master to pricing_strategy/2367_nice_to_have
2b22c0fad2457bcdfd489d72181529201abb2e3d b00a3698efa192b858af39552c9c0c71fdfc3755 RomainM <<EMAIL>> 1742310152 +0100	checkout: moving from pricing_strategy/2367_nice_to_have to master
b00a3698efa192b858af39552c9c0c71fdfc3755 b00a3698efa192b858af39552c9c0c71fdfc3755 RomainM <<EMAIL>> 1742312572 +0100	checkout: moving from master to pricing_strategy/fix_price_edit
b00a3698efa192b858af39552c9c0c71fdfc3755 a9b7a653a128a729aa6a9590410c0222c90eb86f RomainM <<EMAIL>> 1742312607 +0100	commit: feat[support#2367]: fix price editing when a strategy is running
a9b7a653a128a729aa6a9590410c0222c90eb86f 05d9bf9f25aa0d11cd23f3f4fe73d022120ca67c RomainM <<EMAIL>> 1742312971 +0100	commit: feat[support#2367]: test on tooltip
05d9bf9f25aa0d11cd23f3f4fe73d022120ca67c 2cff62563831b78cd340f4e1572d41b022e17153 RomainM <<EMAIL>> 1742476712 +0100	checkout: moving from pricing_strategy/fix_price_edit to pricing_strategy/perf
2cff62563831b78cd340f4e1572d41b022e17153 05d9bf9f25aa0d11cd23f3f4fe73d022120ca67c RomainM <<EMAIL>> 1742483892 +0100	checkout: moving from pricing_strategy/perf to pricing_strategy/fix_price_edit
05d9bf9f25aa0d11cd23f3f4fe73d022120ca67c 053f0072fa390d6cfd7b24cfe34d4ea33a2d2fbb RomainM <<EMAIL>> 1742484244 +0100	commit: feat[support#2367]: fix with paintroller
053f0072fa390d6cfd7b24cfe34d4ea33a2d2fbb b00a3698efa192b858af39552c9c0c71fdfc3755 RomainM <<EMAIL>> 1742485736 +0100	checkout: moving from pricing_strategy/fix_price_edit to master
b00a3698efa192b858af39552c9c0c71fdfc3755 df90e6b9b9c375ca838e28651e8db349ef4f9b34 RomainM <<EMAIL>> 1742485743 +0100	rebase (start): checkout origin/master
df90e6b9b9c375ca838e28651e8db349ef4f9b34 df90e6b9b9c375ca838e28651e8db349ef4f9b34 RomainM <<EMAIL>> 1742485743 +0100	rebase (finish): returning to refs/heads/master
df90e6b9b9c375ca838e28651e8db349ef4f9b34 cb8980b8a8e40e02674f85f2c446a40b0018293b RomainM <<EMAIL>> 1742809320 +0100	rebase (start): checkout origin/master
cb8980b8a8e40e02674f85f2c446a40b0018293b cb8980b8a8e40e02674f85f2c446a40b0018293b RomainM <<EMAIL>> 1742809320 +0100	rebase (finish): returning to refs/heads/master
cb8980b8a8e40e02674f85f2c446a40b0018293b a20ed0a7070475ea52c8dc1514c6670e9014fd91 RomainM <<EMAIL>> 1742812644 +0100	commit: hotfix: wrong stock value retrieved when loading an article
a20ed0a7070475ea52c8dc1514c6670e9014fd91 a20ed0a7070475ea52c8dc1514c6670e9014fd91 RomainM <<EMAIL>> 1742901346 +0100	checkout: moving from master to article_v2/subtabs_localstorage
a20ed0a7070475ea52c8dc1514c6670e9014fd91 b00531fe53408cad6564a28666e84b92036f693d RomainM <<EMAIL>> 1742901378 +0100	commit: fix: copy value now also accepts number
b00531fe53408cad6564a28666e84b92036f693d 620ca4fa1ee1ff17c2425f2f15e43cbae1c424ab RomainM <<EMAIL>> 1742901445 +0100	commit: fix: props icon now accept only Object
620ca4fa1ee1ff17c2425f2f15e43cbae1c424ab 6c8c891a77f9c3cafa2ad483d2da14a4e531f397 RomainM <<EMAIL>> 1742901493 +0100	commit: fix: remove unused const
6c8c891a77f9c3cafa2ad483d2da14a4e531f397 2ead752ba81c23db2e724b12c0d3c1a33f9f7cb6 RomainM <<EMAIL>> 1742901526 +0100	commit: fix: add redirect when switching tabs
2ead752ba81c23db2e724b12c0d3c1a33f9f7cb6 a20ed0a7070475ea52c8dc1514c6670e9014fd91 RomainM <<EMAIL>> 1742916081 +0100	checkout: moving from article_v2/subtabs_localstorage to master
a20ed0a7070475ea52c8dc1514c6670e9014fd91 2ead752ba81c23db2e724b12c0d3c1a33f9f7cb6 RomainM <<EMAIL>> 1742976418 +0100	checkout: moving from master to article_v2/subtabs_localstorage
2ead752ba81c23db2e724b12c0d3c1a33f9f7cb6 b7f48d52c63d34d59d90bc4ca23d4ce5b38f6cfc RomainM <<EMAIL>> 1742978252 +0100	commit: fix: retour review
b7f48d52c63d34d59d90bc4ca23d4ce5b38f6cfc a20ed0a7070475ea52c8dc1514c6670e9014fd91 RomainM <<EMAIL>> 1742981136 +0100	checkout: moving from article_v2/subtabs_localstorage to master
a20ed0a7070475ea52c8dc1514c6670e9014fd91 b7f48d52c63d34d59d90bc4ca23d4ce5b38f6cfc RomainM <<EMAIL>> 1742982141 +0100	checkout: moving from master to article_v2/subtabs_localstorage
b7f48d52c63d34d59d90bc4ca23d4ce5b38f6cfc 2ead752ba81c23db2e724b12c0d3c1a33f9f7cb6 RomainM <<EMAIL>> 1742984411 +0100	checkout: moving from article_v2/subtabs_localstorage to 2ead752ba81c23db2e724b12c0d3c1a33f9f7cb6
2ead752ba81c23db2e724b12c0d3c1a33f9f7cb6 b7f48d52c63d34d59d90bc4ca23d4ce5b38f6cfc RomainM <<EMAIL>> 1742984456 +0100	checkout: moving from 2ead752ba81c23db2e724b12c0d3c1a33f9f7cb6 to b7f48d52c63d34d59d90bc4ca23d4ce5b38f6cfc
b7f48d52c63d34d59d90bc4ca23d4ce5b38f6cfc b7f48d52c63d34d59d90bc4ca23d4ce5b38f6cfc RomainM <<EMAIL>> 1742984463 +0100	checkout: moving from b7f48d52c63d34d59d90bc4ca23d4ce5b38f6cfc to article_v2/subtabs_localstorage
b7f48d52c63d34d59d90bc4ca23d4ce5b38f6cfc e95f6b100f521e3106c30edd2048947291bcbbf0 RomainM <<EMAIL>> 1742986616 +0100	commit: fix: retour review
e95f6b100f521e3106c30edd2048947291bcbbf0 ebbf1f170df9be9e0ea92198147a55759e2c5eb6 RomainM <<EMAIL>> 1742999213 +0100	commit: fix: feedback review
ebbf1f170df9be9e0ea92198147a55759e2c5eb6 a20ed0a7070475ea52c8dc1514c6670e9014fd91 RomainM <<EMAIL>> 1742999601 +0100	checkout: moving from article_v2/subtabs_localstorage to master
a20ed0a7070475ea52c8dc1514c6670e9014fd91 1db27b9838accb5fb4c62bce0d23d314abc1ad2b RomainM <<EMAIL>> 1742999609 +0100	rebase (start): checkout origin/master
1db27b9838accb5fb4c62bce0d23d314abc1ad2b 1db27b9838accb5fb4c62bce0d23d314abc1ad2b RomainM <<EMAIL>> 1742999609 +0100	rebase (finish): returning to refs/heads/master
1db27b9838accb5fb4c62bce0d23d314abc1ad2b 6eba3461d5985e6f0471a833581f1782eb2d6132 RomainM <<EMAIL>> 1742999614 +0100	merge refs/heads/article_v2/subtabs_localstorage: Merge made by the 'ort' strategy.
6eba3461d5985e6f0471a833581f1782eb2d6132 c8162bae8bfbd903da1383f32ab1e5b5ab1c5692 RomainM <<EMAIL>> 1743429714 +0200	rebase (start): checkout origin/master
c8162bae8bfbd903da1383f32ab1e5b5ab1c5692 c8162bae8bfbd903da1383f32ab1e5b5ab1c5692 RomainM <<EMAIL>> 1743429715 +0200	rebase (finish): returning to refs/heads/master
c8162bae8bfbd903da1383f32ab1e5b5ab1c5692 fb73667ba7055d81d4549fc9c68336968876e172 RomainM <<EMAIL>> 1743500047 +0200	merge origin/master: Fast-forward
fb73667ba7055d81d4549fc9c68336968876e172 fb73667ba7055d81d4549fc9c68336968876e172 RomainM <<EMAIL>> 1743500058 +0200	checkout: moving from master to 2370_customer_order_api
fb73667ba7055d81d4549fc9c68336968876e172 f22fe1b1998136971129b3f5c88af1725451b421 RomainM <<EMAIL>> 1743500104 +0200	commit: feat[support#2370]: new API postCloneCustomerOrder
f22fe1b1998136971129b3f5c88af1725451b421 b542fcc3f33b04d6c11bf3e05af159684e5afc22 RomainM <<EMAIL>> 1743500261 +0200	commit: feat[support#2370]: call api cloneCustomerOrder on message sent by legacy
b542fcc3f33b04d6c11bf3e05af159684e5afc22 71f170280c1b8a26914b2df6464eb377e6c37c7e RomainM <<EMAIL>> 1745330260 +0200	checkout: moving from 2370_customer_order_api to master
71f170280c1b8a26914b2df6464eb377e6c37c7e 71f170280c1b8a26914b2df6464eb377e6c37c7e RomainM <<EMAIL>> 1745330269 +0200	checkout: moving from master to 2370_release
71f170280c1b8a26914b2df6464eb377e6c37c7e b542fcc3f33b04d6c11bf3e05af159684e5afc22 RomainM <<EMAIL>> 1745330310 +0200	checkout: moving from 2370_release to 2370_customer_order_api
b542fcc3f33b04d6c11bf3e05af159684e5afc22 7c65b307ff63c8a180910ca39c4de6c3100ae071 RomainM <<EMAIL>> 1745330375 +0200	commit (merge): Merge branch 'refs/heads/2370_release' into 2370_customer_order_api
7c65b307ff63c8a180910ca39c4de6c3100ae071 0124e79e1cd8f910691e7847f4e121bf9aa991d8 RomainM <<EMAIL>> 1745416036 +0200	checkout: moving from 2370_customer_order_api to validation
0124e79e1cd8f910691e7847f4e121bf9aa991d8 cff6c9e7c2619a60fcfc0bbdc4872c70d6088c82 RomainM <<EMAIL>> 1745416042 +0200	merge 2370_release: Merge made by the 'ort' strategy.
cff6c9e7c2619a60fcfc0bbdc4872c70d6088c82 71f170280c1b8a26914b2df6464eb377e6c37c7e RomainM <<EMAIL>> 1745420936 +0200	checkout: moving from validation to master
71f170280c1b8a26914b2df6464eb377e6c37c7e 2b29ccafb8e79902a18f5a7e9c432dd85a28a661 RomainM <<EMAIL>> 1745420950 +0200	rebase (start): checkout origin/master
2b29ccafb8e79902a18f5a7e9c432dd85a28a661 2b29ccafb8e79902a18f5a7e9c432dd85a28a661 RomainM <<EMAIL>> 1745420950 +0200	rebase (finish): returning to refs/heads/master
2b29ccafb8e79902a18f5a7e9c432dd85a28a661 2b29ccafb8e79902a18f5a7e9c432dd85a28a661 RomainM <<EMAIL>> 1745420973 +0200	checkout: moving from master to validation
2b29ccafb8e79902a18f5a7e9c432dd85a28a661 f6bfd106b6c5c16628cb756aaa1081f82f5d9405 RomainM <<EMAIL>> 1745420987 +0200	merge 2370_customer_order_api: Merge made by the 'ort' strategy.
f6bfd106b6c5c16628cb756aaa1081f82f5d9405 ab167a4038d07f5eae2223db1921936b26b186a8 RomainM <<EMAIL>> 1745484953 +0200	checkout: moving from validation to 2370_release
ab167a4038d07f5eae2223db1921936b26b186a8 2b29ccafb8e79902a18f5a7e9c432dd85a28a661 RomainM <<EMAIL>> 1745853774 +0200	checkout: moving from 2370_release to master
2b29ccafb8e79902a18f5a7e9c432dd85a28a661 f02eabb60605017596fe8fa6c9e20a6743e956d4 RomainM <<EMAIL>> 1745853794 +0200	rebase (start): checkout origin/master
f02eabb60605017596fe8fa6c9e20a6743e956d4 f02eabb60605017596fe8fa6c9e20a6743e956d4 RomainM <<EMAIL>> 1745853795 +0200	rebase (finish): returning to refs/heads/master
f02eabb60605017596fe8fa6c9e20a6743e956d4 6b7424affee47eff61ecca10ff7ab1211da15f6c RomainM <<EMAIL>> 1746454956 +0200	pull origin: Fast-forward
6b7424affee47eff61ecca10ff7ab1211da15f6c 3208207416d2390a49dce24cccce164cff9e0258 RomainM <<EMAIL>> 1747923787 +0200	rebase (start): checkout origin/master
3208207416d2390a49dce24cccce164cff9e0258 3208207416d2390a49dce24cccce164cff9e0258 RomainM <<EMAIL>> 1747923787 +0200	rebase (finish): returning to refs/heads/master
3208207416d2390a49dce24cccce164cff9e0258 e79cd58efb58ab2ee3cb7b4cbab5b07e8085bbd1 RomainM <<EMAIL>> 1749127727 +0200	rebase (start): checkout origin/master
e79cd58efb58ab2ee3cb7b4cbab5b07e8085bbd1 e79cd58efb58ab2ee3cb7b4cbab5b07e8085bbd1 RomainM <<EMAIL>> 1749127728 +0200	rebase (finish): returning to refs/heads/master
e79cd58efb58ab2ee3cb7b4cbab5b07e8085bbd1 b1812f787e98c41183bed7a6e60e74f6cc554599 RomainM <<EMAIL>> 1750684364 +0200	rebase (start): checkout origin/master
b1812f787e98c41183bed7a6e60e74f6cc554599 b1812f787e98c41183bed7a6e60e74f6cc554599 RomainM <<EMAIL>> 1750684364 +0200	rebase (finish): returning to refs/heads/master
b1812f787e98c41183bed7a6e60e74f6cc554599 da0e01fbb9a000a63cd1df5bb0bfcca5b533444d RomainM <<EMAIL>> 1752847472 +0200	rebase (start): checkout origin/master
da0e01fbb9a000a63cd1df5bb0bfcca5b533444d da0e01fbb9a000a63cd1df5bb0bfcca5b533444d RomainM <<EMAIL>> 1752847473 +0200	rebase (finish): returning to refs/heads/master
da0e01fbb9a000a63cd1df5bb0bfcca5b533444d da0e01fbb9a000a63cd1df5bb0bfcca5b533444d RomainM <<EMAIL>> 1752852183 +0200	checkout: moving from master to support_2487
da0e01fbb9a000a63cd1df5bb0bfcca5b533444d 2580f126ed052bde9a28608f0c27745353f1d2f5 RomainM <<EMAIL>> 1752852220 +0200	commit: feat[support#2487]: new permission to edit an article sales channel
2580f126ed052bde9a28608f0c27745353f1d2f5 da0e01fbb9a000a63cd1df5bb0bfcca5b533444d RomainM <<EMAIL>> 1754038164 +0200	checkout: moving from support_2487 to master
da0e01fbb9a000a63cd1df5bb0bfcca5b533444d 4f9cbd22eaa2449a1f36b7c50e74db8eb9d22cfc RomainM <<EMAIL>> 1754038174 +0200	pull origin: Fast-forward
4f9cbd22eaa2449a1f36b7c50e74db8eb9d22cfc 4f9cbd22eaa2449a1f36b7c50e74db8eb9d22cfc RomainM <<EMAIL>> 1754056078 +0200	checkout: moving from master to support_2487_2
4f9cbd22eaa2449a1f36b7c50e74db8eb9d22cfc 36477b5b33088449f26701c891519c86548a95e5 RomainM <<EMAIL>> 1754321584 +0200	commit: feat[support#2487]: dissociate permissions for sales channel
36477b5b33088449f26701c891519c86548a95e5 e9539def190f391430471825ae80456fd5207d68 RomainM <<EMAIL>> 1754321971 +0200	checkout: moving from support_2487_2 to validation
e9539def190f391430471825ae80456fd5207d68 2b0006937dd2473ba30a0f7ea4a4d1e7e75eab8d RomainM <<EMAIL>> 1754321975 +0200	merge support_2487_2: Merge made by the 'ort' strategy.
2b0006937dd2473ba30a0f7ea4a4d1e7e75eab8d 36477b5b33088449f26701c891519c86548a95e5 RomainM <<EMAIL>> 1754486596 +0200	checkout: moving from validation to support_2487_2
36477b5b33088449f26701c891519c86548a95e5 4bfa56629ca611926de54710eaca40d16b270d29 RomainM <<EMAIL>> 1754495669 +0200	checkout: moving from support_2487_2 to master
4bfa56629ca611926de54710eaca40d16b270d29 676716a02cc062be00a0820a783266dcf470157d RomainM <<EMAIL>> 1754495677 +0200	commit: feat[support#2487]: dissociate permissions for sales channel
676716a02cc062be00a0820a783266dcf470157d 77fb66e8a29c85cad8f9e17a080f9f75a27feca5 RomainM <<EMAIL>> 1754499040 +0200	commit: feat[support#2487]: dissociate permissions for sales channel
77fb66e8a29c85cad8f9e17a080f9f75a27feca5 77fb66e8a29c85cad8f9e17a080f9f75a27feca5 RomainM <<EMAIL>> 1754576345 +0200	checkout: moving from master to support_2305
77fb66e8a29c85cad8f9e17a080f9f75a27feca5 9503f714a47efbbf5511fde10a87375195c95836 RomainM <<EMAIL>> 1755068193 +0200	commit: feat[support#2305]: associate a buyer to a subcategory
9503f714a47efbbf5511fde10a87375195c95836 c04782967669e362b33d133b4dc7005b50a42e8a RomainM <<EMAIL>> 1755528440 +0200	commit: feat[support#2305]: fixed buyers
c04782967669e362b33d133b4dc7005b50a42e8a a1f78730f856e24a0e396d55953cde1d628ab025 RomainM <<EMAIL>> 1755763524 +0200	checkout: moving from support_2305 to master
a1f78730f856e24a0e396d55953cde1d628ab025 2741497faa588ab165ef66cf2c5ed081c22e662f RomainM <<EMAIL>> 1755763536 +0200	pull origin: Fast-forward
2741497faa588ab165ef66cf2c5ed081c22e662f a065d168037b6499f0cc670bb80cc20d0259fd4d RomainM <<EMAIL>> 1756393806 +0200	rebase (start): checkout origin/master
a065d168037b6499f0cc670bb80cc20d0259fd4d a065d168037b6499f0cc670bb80cc20d0259fd4d RomainM <<EMAIL>> 1756393807 +0200	rebase (finish): returning to refs/heads/master
a065d168037b6499f0cc670bb80cc20d0259fd4d 1e14a87908e0c14cc39eea85e3d348a700fbfeb8 RomainM <<EMAIL>> 1757345717 +0200	merge origin/master: Fast-forward
1e14a87908e0c14cc39eea85e3d348a700fbfeb8 388e9fe5bb20815f168f7806df6b2a29f6bf7148 RomainM <<EMAIL>> 1757692343 +0200	merge origin/master: Fast-forward
388e9fe5bb20815f168f7806df6b2a29f6bf7148 388e9fe5bb20815f168f7806df6b2a29f6bf7148 RomainM <<EMAIL>> 1757692408 +0200	checkout: moving from master to customer_order_margin
388e9fe5bb20815f168f7806df6b2a29f6bf7148 8b6b7591ffd03d04c080ed0dadb1b3a810ff178c RomainM <<EMAIL>> 1757694194 +0200	commit: feat[support#2503]: add slide in to show margins by products in order
