# Tests pour CustomerOrderProductsMargin

Ce document décrit les tests créés pour le composant `CustomerOrderProductsMargin.vue`.

## Fichiers de test créés

### 1. Test e2e Cypress
**Fichier** : `tests/e2e/specs/customer_order/customer_order_products_margin.e2e.js`

**Description** : Tests end-to-end utilisant Cypress pour tester le composant dans le contexte complet de l'application.

**Couverture** :
- ✅ Affichage du panneau latéral avec les marges produits
- ✅ Vérification des en-têtes de colonnes du tableau
- ✅ Affichage correct des données produits (SKU, nom, quantité, prix, remise, marge)
- ✅ Calculs des totaux (quantité, prix de vente, remise, marge)
- ✅ Formatage des devises (format français avec €)
- ✅ Affichage des images et liens des articles
- ✅ Gestion de l'état vide (aucun produit)
- ✅ Gestion des erreurs API
- ✅ État de chargement

### 2. Fixture de données
**Fichier** : `tests/e2e/fixtures/erp/customer-order-products/customer_order_products_margin.json`

**Description** : Données de test réalistes pour les tests e2e.

**Contenu** :
- 3 produits avec des données complètes (marges, prix, remises)
- Structure conforme à l'API réelle
- Données calculées pour vérifier les totaux

### 3. Histoire (Storybook)
**Fichier** : `src/apps/erp/components/CustomerOrder/CustomerOrderProductsMargin.story.vue`

**Description** : Documentation visuelle et tests manuels du composant.

**Variantes** :
- ✅ État par défaut
- ✅ Avec beaucoup de produits
- ✅ Commande vide
- ✅ État de chargement

## Exécution des tests

### Tests e2e (Cypress)
```bash
npm run e2e tests/e2e/specs/customer_order/customer_order_products_margin.e2e.js
```

### Histoire (Histoire/Storybook)
```bash
npm run story:dev
```
Puis naviguer vers `CustomerOrder/CustomerOrderProductsMargin`

## Données de test

### Structure des données de test (fixture)
```json
{
    "status": "success",
    "data": {
        "customer_order_products": [
            {
                "customer_order_product_id": 1,
                "sku": "JAMOS809HCSBNR",
                "article_name": "S809 HCS Bois noir",
                "quantity": 2,
                "discount_amount": 15.50,
                "margin_details": [
                    {
                        "selling_price_tax_included": 299.99,
                        "margin_tax_included": 95.25
                    }
                ]
            }
            // ... autres produits
        ]
    }
}
```

### Calculs attendus (basés sur la fixture)
- **Produit 1** : Prix total = 299,99 × 2 = 599,98€, Marge totale = 95,25 × 2 = 190,50€
- **Produit 2** : Prix total = 199,99 × 1 = 199,99€, Marge totale = 65,50 × 1 = 65,50€
- **Produit 3** : Prix total = 12,99 × 3 = 38,97€, Marge totale = 7,85 × 3 = 23,55€
- **Total** : Quantité = 6, Prix = 838,94€, Remise = 26,00€, Marge = 279,55€

## Couverture des tests

### Fonctionnalités testées
- [x] Ouverture du panneau latéral via événement `open-products-margin`
- [x] Chargement des données depuis l'API
- [x] Affichage du tableau avec les bonnes colonnes
- [x] Calculs des totaux (quantité, prix, remise, marge)
- [x] Formatage des devises (format français)
- [x] Affichage des informations produits (SKU, nom, image)
- [x] Gestion des erreurs API avec toast
- [x] État de chargement
- [x] État vide (aucun produit)

### Cas de test couverts
- [x] Affichage normal avec 3 produits
- [x] Calculs corrects des totaux
- [x] Formatage des devises
- [x] Liens et images des articles
- [x] Erreur API (500)
- [x] Réponse vide
- [x] État de chargement avec délai

## Intégration dans l'application

### Contexte d'utilisation
Le composant est utilisé dans un panneau latéral (`slide-out-container`) déclenché par l'événement `open-products-margin` depuis la page d'édition de commande client.

### Navigation
- Page : `/legacy/v1/commandes/edition_commande.php?id_commande=123456`
- Événement : `cy.emitEvent('open-products-margin', 123456)`
- Panneau : `[data-context=slide-out-container]`

### APIs mockées
- `GET **/api/erp/v1/customer-order/123456/for-edition-page` : Données de la commande
- `POST **/api/erp/v1/customer-order-products` : Produits avec marges

## Notes importantes

1. **Tests e2e** : Les tests utilisent Cypress pour tester l'intégration complète dans l'application
2. **Données réalistes** : La fixture contient des données conformes à l'API réelle
3. **Calculs vérifiés** : Tous les calculs de marges et totaux sont testés avec des valeurs précises
4. **Formatage français** : Les devises sont testées avec le formatage français (virgule décimale, espace milliers, €)
5. **Contexte d'application** : Les tests reproduisent le flux utilisateur complet

## Maintenance

Pour maintenir ces tests :
1. Mettre à jour la fixture si la structure API change
2. Ajouter de nouveaux cas de test pour les nouvelles fonctionnalités
3. Vérifier les calculs si la logique métier évolue
4. Maintenir la cohérence avec les autres tests e2e du projet
5. Mettre à jour la documentation
