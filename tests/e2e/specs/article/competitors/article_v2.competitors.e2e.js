context('With a regular article', () => {
    beforeEach(() => {
        cy.intercept('GET', /\/api\/erp\/v2\/article\/(KEFQ350NR|117735)$/, {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')
    })

    const visitPage = (permissions, page) => {
        cy.authenticate()
        cy.mockErpUser([...(permissions ?? [])])
        cy.visit(page ?? '/articles/KEFQ350NR/competitors')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()
        cy.closeAllToasts()
    }

    describe('with correct api response', () => {
        beforeEach(() => {
            cy.intercept('GET', '**/api/erp/v1/article/117735/lowest-competitor-prices', {
                fixture: 'erp/easylounge/get_product_prices__KEFQ350NR',
            }).as('get_lowest_competitor_prices')
        })

        it('should show competitors table', () => {
            visitPage()
            cy.wait('@get_lowest_competitor_prices')

            cy.get('[data-context=article-v2-competitors]')
                .should('be.visible')
                .within(() => {
                    const headers = ['Concurrent', 'Marketplace', 'Prix TTC', 'Dispo', 'Date', '', 'Suivi']
                    cy.get('th[scope=col]').should('have.length', headers.length)

                    headers.forEach((text, idx) => {
                        cy.get('th[scope=col]').eq(idx).should('contain', text)
                    })

                    cy.get('tbody tr').checkRows([
                        [
                            [
                                'SONVIDEO',
                                {
                                    context: 'erp-link',
                                    should: 'have.attr',
                                    method: 'href',
                                    value: 'https://dereferer.me/?https%3A%2F%2Fwww.son-video.com%2Farticle%2Fnomade-casques-et-ecouteurs-casques-bluetooth%2Fbose%2Fquietcomfort-ultra-blanc',
                                },
                            ],
                            'SONVIDEO',
                            '399,00 €',
                            [
                                'OUI',
                                {
                                    context: 'status-indicator',
                                    should: 'have.class',
                                    value: 'bg-green-500',
                                },
                            ],
                            '18/02/2025 19:43',
                            'Exclu par le moteur',
                        ],
                        [
                            [
                                'GETGOODS',
                                {
                                    context: 'erp-link',
                                    should: 'have.attr',
                                    method: 'href',
                                    value: 'https://dereferer.me/?https%3A%2F%2Fwww.fnac.com%2FCasque-sans-fil-Bose-QuietComfort-Ultra-a-reduction-de-bruit-Blanc%2Fa18438843%2Fw-4',
                                },
                            ],
                            'FNAC',
                            '399,99 €',
                            [
                                'NON',
                                {
                                    context: 'status-indicator',
                                    should: 'have.class',
                                    value: 'bg-red-500',
                                },
                            ],
                            '19/02/2025 01:21',
                            'Le moins cher',
                            [
                                '24h',
                                {
                                    should: 'be.visible',
                                    context: 'erp-button',
                                },
                            ],
                        ],
                        [
                            [
                                'GETGOODS',
                                {
                                    context: 'erp-link',
                                    should: 'have.attr',
                                    method: 'href',
                                    value: 'https://dereferer.me/?https%3A%2F%2Fwww.cdiscount.com%2Fhigh-tech%2Fcasques-baladeur-hifi%2Fcasque-arceau-a-reduction-de-bruit-audio-spatial%2Ff-1065420-bos1723079459508.html',
                                },
                            ],
                            'CDISCOUNT',
                            '472,35 €',
                            [
                                'OUI',
                                {
                                    context: 'status-indicator',
                                    should: 'have.class',
                                    value: 'bg-green-500',
                                },
                            ],
                            '19/02/2025 05:12',
                            '',
                            [
                                '24h',
                                {
                                    should: 'be.visible',
                                    context: 'erp-button',
                                },
                                `Non suivi jusqu'au 19/02/2025 05:12:50`,
                            ],
                        ],
                    ])
                })

            cy.get(
                '[data-context=article-v2-competitors] tbody tr:eq(1) td:eq(1) [data-context=erp-expand-trigger]',
            ).click()
            cy.get(
                '[data-context=article-v2-competitors] tbody tr:eq(3) td:eq(1) [data-context=erp-expand-trigger]',
            ).click()
            cy.get('[data-context=article-v2-competitors] tbody tr').should('have.length', 6)
        })
    })

    describe('with api failure', () => {
        it('should show a message when the api do not have the product', () => {
            cy.intercept('GET', '**/api/erp/v1/article/117735/lowest-competitor-prices', {
                statusCode: 404,
            }).as('get_lowest_competitor_prices')

            visitPage()
            cy.wait('@get_lowest_competitor_prices')

            cy.get('[data-context=article-v2-competitors]').within(() => {
                cy.get('tbody tr').should('have.length', 1)
                cy.get('[data-context=table-no-result]')
                    .should('be.visible')
                    .should('contain', "Aucun prix n'a été trouvé pour le produit KEFQ350NR.")
            })
        })
    })
})
