import { defineConfig } from 'histoire'
import { HstVue } from '@histoire/plugin-vue2'
import { tailwindTokens } from 'histoire/dist/node/builtin-plugins/tailwind-tokens'
import { parse } from 'vue-docgen-api'
import { createFilter } from '@rollup/pluginutils'

function vitePluginDocgen() {
    const options = {
        /* Regex (optional) - The file path pattern to match */
        include: /\.vue$/,

        /* Regex (optional) - The file path pattern to exclude */
        exclude: /\.story\.vue$/,

        /* String (optional) - The property name to inject the docgen metadata at */
        injectAt: '__docgenInfo',

        /* Object (optional) - Specific Docgen API options */
        docgenOptions: {
            jsx: false,
        },
    }

    const filter = createFilter(options.include, options.exclude)

    return {
        name: 'vite-plugin-docgen',
        enforce: 'post',

        async transform(source, id) {
            if (!filter(id)) {
                return
            }

            let metaData = {}

            try {
                metaData = await parse(id, options.docgenOptions)
            } catch (error) {
                console.warn(error)
            }

            return `${source};_sfc_main.${options.injectAt} = ${JSON.stringify(metaData)}`
        },
    }
}

// see config reference https://histoire.dev/reference/config.html
export default defineConfig({
    plugins: [HstVue(), tailwindTokens({ configFile: '' })],
    setupFile: '.histoire/histoire.setup.js',
    tree: {
        groups: [{ id: 'top', title: '' }],
    },
    theme: {
        title: 'Composants ERP Client',
        logo: {
            square: './public/images/svd_logo_s.png',
            dark: './public/images/svd_logo_white_line.png',
        },
        defaultColorScheme: 'dark',
        hideColorSchemeSwitch: true,
        darkClass: '', // CSS Class applied on each story, default to "dark", which is not what we want
    },
    defaultStoryProps: {
        icon: 'carbon:thumbnail-1',
        iconColor: '#9522b6',
        layout: {
            type: 'grid',
            width: '100%',
        },
    },
    backgroundPresets: [
        {
            label: 'Blanc',
            color: '#fff',
            contrastColor: 'rgb(55, 69, 98)',
        },
        {
            label: 'Clair',
            color: '#f9fafb',
            contrastColor: 'rgb(71, 85, 105)',
        },
        {
            label: 'Bleu SVD',
            color: '#3366cc',
            contrastColor: '#fff',
        },
        {
            label: 'Menu Background',
            color: '#23252a',
            contrastColor: 'rgb(169, 172, 177)',
        },
    ],
    autoApplyContrastColor: true,
    vite: {
        plugins: [vitePluginDocgen()],
    },
})
