const { defineConfig } = require('cypress')

module.exports = defineConfig({
  projectId: 'xhhjgv',
    video: false,
    chromeWebSecurity: false,
    viewportWidth: 1440,
    viewportHeight: 900,
    blockHosts: ['*google-analytics.com', '*googletagmanager.com'],
    retries: {
        runMode: 3,
        openMode: 0,
    },
    env: {
        FAIL_FAST_ENABLED: false,
    },
    e2e: {
        // We've imported your old cypress plugins here.
        // You may want to clean this up later by importing these.
        setupNodeEvents(cypress_on, config) {
            const on = require('cypress-on-fix')(cypress_on, config)

            require('cypress-split')(on, config)
            require('cypress-fail-fast/plugin')(on, config)

            return config
        },
        baseUrl: 'http://localhost:8081',
        specPattern: 'tests/e2e/**/*.e2e.js',
        supportFile: 'tests/e2e/support/e2e.js',
        fixturesFolder: 'tests/e2e/fixtures',
        screenshotsFolder: 'tests/e2e/screenshots',
        videosFolder: 'tests/e2e/videos',
    },
    component: {
        devServer: {
            framework: 'vue',
            bundler: 'vite',
        },
        specPattern: 'src/**/*.cy.js',
        supportFile: 'tests/component/support.js',
        indexHtmlFile: 'tests/component/index.html',
        fixturesFolder: 'tests/e2e/fixtures',
    },
})
