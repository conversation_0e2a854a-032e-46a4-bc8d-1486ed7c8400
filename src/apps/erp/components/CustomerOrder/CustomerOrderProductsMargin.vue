<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { cPostCustomerOrderProducts, type CustomerOrderProduct } from '@/shared/api/erp_server'
import { useToastStore } from '@/services/plugin/toast/stores'
import { ErpTable } from '@/shared/components/ErpTable'
import PageHeader from '@/shared/components/ui/PageHeader.vue'
import ErpArticleItem from '@/shared/components/article/ErpArticleItem.vue'
import { formatCurrency } from '@/shared/string_utils'

interface Props {
    customerOrderId: number
}

interface CustomerOrderProductMargin {
    customer_order_product_id: number
    article_name: string
    article_image: string
    sku: string
    quantity: number
    selling_price_tax_included: number
    discount_amount: number
    margin_tax_included: number
}

const props = withDefaults(defineProps<Props>(), {})

const is_loading = ref<boolean>(false)
const products = ref<CustomerOrderProduct[]>([])

const columns = ref([
    {
        name: 'article',
        title: 'Produit',
    },
    {
        name: 'quantity',
        title: 'Qté',
        _columnClasses: 'whitespace-nowrap text-right',
    },
    {
        name: 'selling_price',
        title: 'Prix de vente TTC',
        _columnClasses: 'whitespace-nowrap text-right',
    },
    {
        name: 'discount',
        title: 'Remise TTC',
        _columnClasses: 'whitespace-nowrap text-right',
    },
    {
        name: 'margin',
        title: 'Marge TTC',
        _columnClasses: 'whitespace-nowrap text-right',
    },
    {
        name: 'dummy',
        title: '',
    },
])

const params = computed(() => {
    return {
        where: {
            _and: [
                {
                    customer_order_id: {
                        _eq: props.customerOrderId,
                    },
                },
            ],
        },
        included_dependencies: ['margin_details'],
        limit: 9999,
    }
})

const computedProducts = computed<CustomerOrderProductMargin[]>(() => {
    return products.value.map((p: CustomerOrderProduct) => {
        return {
            customer_order_product_id: p.customer_order_product_id,
            article_name: p.article_name,
            article_image: p.article_image,
            sku: p.sku,
            quantity: p.quantity,
            selling_price_tax_included: p.margin_details[0].selling_price_tax_included * p.quantity,
            discount_amount: p.discount_amount,
            margin_tax_included: p.margin_details[0].margin_tax_included * p.quantity,
        }
    })
})

// Computed property pour ajouter une ligne de total
const productsWithTotal = computed<CustomerOrderProductMargin[]>(() => {
    if (!products.value || products.value.length === 0) return []

    // Calculer les totaux
    const totals = computedProducts.value.reduce(
        (acc, product) => {
            return {
                quantity: acc.quantity + product.quantity,
                selling_price_tax_included: acc.selling_price_tax_included + product.selling_price_tax_included,
                discount_amount: acc.discount_amount + product.discount_amount,
                margin_tax_included: acc.margin_tax_included + product.margin_tax_included,
            }
        },
        {
            quantity: 0,
            selling_price_tax_included: 0,
            discount_amount: 0,
            margin_tax_included: 0,
        },
    )

    // Créer l'élément de total
    const totalRow: CustomerOrderProductMargin = {
        article_image: '',
        sku: '',
        customer_order_product_id: -1, // ID spécial pour identifier la ligne de total
        article_name: 'TOTAL', // ID spécial pour identifier la ligne de total
        quantity: totals.quantity,
        selling_price_tax_included: totals.selling_price_tax_included,
        discount_amount: totals.discount_amount,
        margin_tax_included: totals.margin_tax_included,
    }

    return [...computedProducts.value, totalRow]
})

/**
 * Fetch all customer order products
 */
const fetchData = async (): Promise<void> => {
    try {
        is_loading.value = true
        const response = await cPostCustomerOrderProducts(params.value)
        products.value = response.data.customer_order_products
    } catch (error) {
        useToastStore().add({ content: 'Une erreur est survenue lors de la récupération des données' })
    } finally {
        is_loading.value = false
    }
}

onMounted(() => {
    fetchData()
})
</script>

<template>
    <div v-if="!is_loading" data-context="products-margin">
        <page-header>
            <span class="mr-3">Marges produits</span>
        </page-header>

        <erp-table :columns="columns" :rows="productsWithTotal">
            <template #article="{ row }">
                <div v-if="row.customer_order_product_id === -1" class="font-bold text-gray-900">
                    {{ row.article_name }}
                </div>
                <erp-article-item
                    v-else
                    show-actions
                    :sku="row.sku"
                    :name="row.article_name"
                    :image="row.article_image"
                />
            </template>

            <template #quantity="{ row }">
                <span :class="{ 'font-bold text-gray-900': row.article_name === 'TOTAL' }">
                    {{ row.quantity }}
                </span>
            </template>

            <template #selling_price="{ row }">
                <div :class="{ 'font-bold text-gray-900': row.article_name === 'TOTAL' }">
                    {{ formatCurrency(row.selling_price_tax_included) }}
                </div>
            </template>

            <template #discount="{ row }">
                <span :class="{ 'font-bold text-gray-900': row.article_name === 'TOTAL' }">
                    {{ formatCurrency(row.discount_amount) }}
                </span>
            </template>

            <template #margin="{ row }">
                <div :class="{ 'font-bold text-gray-900': row.article_name === 'TOTAL' }">
                    {{ formatCurrency(row.margin_tax_included) }}
                </div>
            </template>
            <template #dummy>
                <span></span>
            </template>
        </erp-table>
    </div>
</template>
