describe('Customer order products margin', () => {
    const VISIT_PAGE = (file) => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {},
            },
        }).as('customer_order')

        cy.intercept('POST', '**/api/erp/v1/customer-order-products', { fixture: file }).as(
            'cpost_customer_order_products',
        )

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait('@customer_order')
        cy.emitEvent('open-products-margin', 123456)
        cy.wait('@cpost_customer_order_products')
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Displays customer order products margins', () => {
        it('opens the margin panel and displays basic structure', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            // Check that slide-out container exists
            cy.get('[data-context=slide-out-container]').should('exist')

            // Check that the products-margin panel exists
            cy.get('[data-context=products-margin]').as('panel').should('exist')

            // Check page header
            cy.get('@panel').find('[data-context=page-header]').should('contain', 'Marges produits')

            // Check that table exists
            cy.get('@panel').find('[data-context=erp-table]').should('exist')
        })

        it('displays correct table headers', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=products-margin]').as('panel')
            cy.get('@panel').find('[data-context=erp-table] thead tr th').as('headers')

            cy.get('@headers').should('have.length', 6)
            cy.get('@headers').eq(0).should('contain', 'Produit')
            cy.get('@headers').eq(1).should('contain', 'Qté')
            cy.get('@headers').eq(2).should('contain', 'Prix de vente TTC')
            cy.get('@headers').eq(3).should('contain', 'Remise TTC')
            cy.get('@headers').eq(4).should('contain', 'Marge TTC')
        })

        it('displays product rows with data', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=products-margin]').as('panel')

            // Should have 4 rows: 3 products + 1 total
            cy.get('@panel').find('[data-context=table-row]').should('have.length', 4)

            // Check first product data
            cy.get('@panel').find('[data-context=table-row]').eq(0).as('firstProduct')
            cy.get('@firstProduct').should('contain', 'JAMOS809HCSBNR')
            cy.get('@firstProduct').should('contain', 'S809 HCS Bois noir')
            cy.get('@firstProduct').should('contain', '2') // quantity
        })

        it('displays total row correctly', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_margin.json')

            cy.get('[data-context=products-margin]').as('panel')

            // Last row should be total
            cy.get('@panel').find('[data-context=table-row]').eq(3).as('totalRow')
            cy.get('@totalRow').should('contain', 'TOTAL')
            cy.get('@totalRow').should('contain', '6') // total quantity: 2+1+3
        })
    })
})
