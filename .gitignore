node_modules
dist

/tests/e2e/videos/
/tests/e2e/screenshots/

# Tests reports
/tests/e2e/results/
mochawesome.json

# Tests dowloaded files
cypress/downloads/*

# local env files
.env.local
.env.*.local
.env.production

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

*.npmrc

# Proxy conf
proxy/proxy-api-custom.conf

/deployment/erp-client/
/deployment/*.tar.gz
/deployment/*.tar
/deployment/deployment*.id
/deployment/version*

#VScode config file
jsconfig.json
