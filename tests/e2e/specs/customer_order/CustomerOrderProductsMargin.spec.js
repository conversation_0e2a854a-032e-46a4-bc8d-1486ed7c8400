import { describe, it, expect, vi, beforeEach } from 'vitest'
import { shallowMount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import CustomerOrderProductsMargin from '@/apps/erp/components/CustomerOrder/CustomerOrderProductsMargin.vue'

// Mock des dépendances externes
vi.mock('@/shared/api/erp_server', () => ({
    cPostCustomerOrderProducts: vi.fn(),
}))

vi.mock('@/shared/string_utils', () => ({
    formatCurrency: vi.fn((value) => `${value} €`),
}))

vi.mock('@/services/plugin/toast/stores', () => ({
    useToastStore: vi.fn(() => ({
        add: vi.fn(),
    })),
}))

describe('CustomerOrderProductsMargin', () => {
    let wrapper
    let pinia
    let mockCPostCustomerOrderProducts
    let mockUseToastStore

    const mockCustomerOrderProducts = [
        {
            customer_order_product_id: 1,
            article_name: 'Produit 1',
            article_image: '/image1.jpg',
            sku: 'SKU001',
            quantity: 2,
            discount_amount: 10,
            margin_details: [
                {
                    selling_price_tax_included: 100,
                    margin_tax_included: 20,
                },
            ],
        },
        {
            customer_order_product_id: 2,
            article_name: 'Produit 2',
            article_image: '/image2.jpg',
            sku: 'SKU002',
            quantity: 1,
            discount_amount: 5,
            margin_details: [
                {
                    selling_price_tax_included: 50,
                    margin_tax_included: 10,
                },
            ],
        },
    ]

    beforeEach(async () => {
        pinia = createPinia()
        setActivePinia(pinia)
        vi.clearAllMocks()

        // Import des mocks après le setup
        const { cPostCustomerOrderProducts } = await import('@/shared/api/erp_server')
        const { useToastStore } = await import('@/services/plugin/toast/stores')

        mockCPostCustomerOrderProducts = cPostCustomerOrderProducts
        mockUseToastStore = useToastStore
    })

    const createWrapper = (props = {}) => {
        return shallowMount(CustomerOrderProductsMargin, {
            props: {
                customerOrderId: 123,
                ...props,
            },
            global: {
                plugins: [pinia],
                stubs: {
                    PageHeader: true,
                    ErpTable: true,
                    ErpArticleItem: true,
                },
            },
        })
    }

    describe('Props', () => {
        it('should accept customerOrderId prop', () => {
            wrapper = createWrapper({ customerOrderId: 456 })
            expect(wrapper.props('customerOrderId')).toBe(456)
        })

        it('should have default customerOrderId', () => {
            wrapper = createWrapper()
            expect(wrapper.props('customerOrderId')).toBe(123)
        })
    })

    describe('Data initialization', () => {
        it('should initialize with correct default values', () => {
            wrapper = createWrapper()
            expect(wrapper.vm.is_loading).toBe(false)
            expect(wrapper.vm.products).toEqual([])
        })

        it('should have correct columns configuration', () => {
            wrapper = createWrapper()
            const expectedColumns = [
                { name: 'article', title: 'Produit' },
                { name: 'quantity', title: 'Qté', _columnClasses: 'whitespace-nowrap text-right' },
                { name: 'selling_price', title: 'Prix de vente TTC', _columnClasses: 'whitespace-nowrap text-right' },
                { name: 'discount', title: 'Remise TTC', _columnClasses: 'whitespace-nowrap text-right' },
                { name: 'margin', title: 'Marge TTC', _columnClasses: 'whitespace-nowrap text-right' },
                { name: 'dummy', title: '' },
            ]
            expect(wrapper.vm.columns).toEqual(expectedColumns)
        })
    })

    describe('Computed properties', () => {
        beforeEach(() => {
            wrapper = createWrapper()
            wrapper.vm.products = mockCustomerOrderProducts
        })

        describe('params', () => {
            it('should generate correct API parameters', () => {
                const expectedParams = {
                    where: {
                        _and: [
                            {
                                customer_order_id: {
                                    _eq: 123,
                                },
                            },
                        ],
                    },
                    included_dependencies: ['margin_details'],
                    limit: 9999,
                }
                expect(wrapper.vm.params).toEqual(expectedParams)
            })
        })

        describe('computedProducts', () => {
            it('should transform products correctly', () => {
                const result = wrapper.vm.computedProducts
                expect(result).toHaveLength(2)
                expect(result[0]).toEqual({
                    customer_order_product_id: 1,
                    article_name: 'Produit 1',
                    article_image: '/image1.jpg',
                    sku: 'SKU001',
                    quantity: 2,
                    selling_price_tax_included: 200, // 100 * 2
                    discount_amount: 10,
                    margin_tax_included: 40, // 20 * 2
                })
            })
        })

        describe('productsWithTotal', () => {
            it('should add total row to products', () => {
                const result = wrapper.vm.productsWithTotal
                expect(result).toHaveLength(3) // 2 products + 1 total row

                const totalRow = result[result.length - 1]
                expect(totalRow).toEqual({
                    customer_order_product_id: -1,
                    article_name: 'TOTAL',
                    article_image: '',
                    sku: '',
                    quantity: 3, // 2 + 1
                    selling_price_tax_included: 250, // 200 + 50
                    discount_amount: 15, // 10 + 5
                    margin_tax_included: 50, // 40 + 10
                })
            })

            it('should return empty array when no products', () => {
                wrapper.vm.products = []
                expect(wrapper.vm.productsWithTotal).toEqual([])
            })
        })
    })

    describe('API calls', () => {
        beforeEach(() => {
            mockCPostCustomerOrderProducts.mockResolvedValue({
                data: {
                    customer_order_products: mockCustomerOrderProducts,
                },
            })
        })

        it('should fetch data on mount', async () => {
            wrapper = createWrapper()
            await wrapper.vm.$nextTick()

            expect(mockCPostCustomerOrderProducts).toHaveBeenCalledWith({
                where: {
                    _and: [
                        {
                            customer_order_id: {
                                _eq: 123,
                            },
                        },
                    ],
                },
                included_dependencies: ['margin_details'],
                limit: 9999,
            })
        })

        it('should handle API success', async () => {
            wrapper = createWrapper()
            await wrapper.vm.fetchData()

            expect(wrapper.vm.products).toEqual(mockCustomerOrderProducts)
            expect(wrapper.vm.is_loading).toBe(false)
        })

        it('should handle API error', async () => {
            const mockToastStore = { add: vi.fn() }
            mockUseToastStore.mockReturnValue(mockToastStore)

            mockCPostCustomerOrderProducts.mockRejectedValue(new Error('API Error'))

            wrapper = createWrapper()
            await wrapper.vm.fetchData()

            expect(mockToastStore.add).toHaveBeenCalledWith({
                content: 'Une erreur est survenue lors de la récupération des données',
            })
            expect(wrapper.vm.is_loading).toBe(false)
        })
    })

    describe('Template rendering', () => {
        beforeEach(() => {
            mockCPostCustomerOrderProducts.mockResolvedValue({
                data: {
                    customer_order_products: mockCustomerOrderProducts,
                },
            })
        })

        it('should not render when loading', async () => {
            wrapper = createWrapper()
            wrapper.vm.is_loading = true
            await wrapper.vm.$nextTick()

            expect(wrapper.find('[data-context="products-availability"]').exists()).toBe(false)
        })

        it('should render page header when not loading', async () => {
            wrapper = createWrapper()
            wrapper.vm.is_loading = false
            await wrapper.vm.$nextTick()

            expect(wrapper.find('[data-context="products-availability"]').exists()).toBe(true)
        })

        it('should pass correct props to ErpTable', async () => {
            wrapper = createWrapper()
            wrapper.vm.is_loading = false
            wrapper.vm.products = mockCustomerOrderProducts
            await wrapper.vm.$nextTick()

            const erpTable = wrapper.findComponent({ name: 'ErpTable' })
            expect(erpTable.exists()).toBe(true)
            expect(erpTable.props('columns')).toEqual(wrapper.vm.columns)
            expect(erpTable.props('rows')).toEqual(wrapper.vm.productsWithTotal)
        })
    })
})
