<script lang="ts" setup>
import { useCustomerOrderStore } from '@/apps/erp/components/CustomerOrder/stores/customer_order'
import {
    type AntiFraudFormattedArticleReason,
    AntiFraudReason,
    AntiFraudReasonArticle,
    AntiFraudStatus,
} from '@/shared/api/erp_server/anti_fraud/types'
import { formatCurrency } from '@/shared/string_utils'
import { faExclamationTriangle } from '@son-video/font-awesome-pro-solid/index.es'
import { storeToRefs } from 'pinia'
import { computed } from 'vue'

const store = useCustomerOrderStore()
const { anti_fraud } = storeToRefs(store)

const anti_fraud_customer_order = computed(() => anti_fraud.value?.statuses.anti_fraud_customer_order)

const rejected_anti_fraud_customer_order_payments = computed(
    () =>
        anti_fraud.value?.statuses.anti_fraud_customer_order_payments.filter((afcop) =>
            [AntiFraudStatus.NON_ELIGIBLE, AntiFraudStatus.REJECTED].includes(afcop.status),
        ) ?? [],
)

const has_a_rejected_anti_fraud_status = computed(
    () =>
        [AntiFraudStatus.NON_ELIGIBLE, AntiFraudStatus.REJECTED].includes(anti_fraud_customer_order.value?.status) ||
        rejected_anti_fraud_customer_order_payments.value.length > 0,
)

function extractAntiFraudMeta(type: string, id: string | number): string {
    return anti_fraud.value?.meta.find((m) => m.type === type && m.id === id).name
}

function formatInvalidArticleDetails(reason: AntiFraudFormattedArticleReason): string {
    const prefix = `<span class="font-medium">${reason.sku} :</span>`

    if (reason.key === AntiFraudReasonArticle.INVALID_SKU) {
        return `${prefix} considéré comme étant produit risqué`
    }

    if (reason.key === AntiFraudReasonArticle.INVALID_BRAND && reason.details?.min_price !== undefined) {
        return `${prefix} Marque ${extractAntiFraudMeta('brand', reason.details.brand_id)} et prix supérieur à ${formatCurrency(reason.details.min_price)}`
    }

    if (reason.key === AntiFraudReasonArticle.INVALID_BRAND && reason.details?.min_price === undefined) {
        return `${prefix}  Marque ${extractAntiFraudMeta('brand', reason.details.brand_id)}`
    }

    if (reason.key === AntiFraudReasonArticle.INVALID_SUBCATEGORY && reason.details?.min_price !== undefined) {
        return `${prefix} Sous-catégorie ${extractAntiFraudMeta('subcategory', reason.details.subcategory_id)} et prix supérieur à ${formatCurrency(reason.details.min_price)}`
    }

    return `${prefix} Sous-catégorie ${extractAntiFraudMeta('subcategory', reason.details.subcategory_id)}`
}
</script>

<template>
    <div
        v-if="has_a_rejected_anti_fraud_status"
        data-context="customer-order-anti-fraud-module"
        class="p-3 border-b bg-amber-50"
    >
        <div class="flex gap-2 items-baseline -my-[5px]">
            <div class="text-amber-600">
                <font-awesome-icon :icon="faExclamationTriangle" fixed-width class="text-sm" />
            </div>
            <div class="flex flex-col">
                <span class="font-medium text-amber-700">Alerte module anti-fraude.</span>

                <!-- Anti Fraud rules applied on customer order -->
                <template v-if="anti_fraud_customer_order">
                    <div v-if="anti_fraud_customer_order.reason.name === AntiFraudReason.CUSTOMER_IS_BLACKLISTED">
                        Le client est blacklisté. Les paiements de la commande ne seront pas acceptés automatiquement.
                    </div>

                    <div
                        v-if="
                            anti_fraud_customer_order.reason.name ===
                            AntiFraudReason.SHIPPING_AND_BILLING_COUNTRIES_ARE_DIFFERENT
                        "
                    >
                        Les pays de facturation et de livraison sont différents. Les paiements de la commande ne seront
                        pas acceptés automatiquement.
                    </div>

                    <div
                        v-if="
                            anti_fraud_customer_order.reason.name ===
                            AntiFraudReason.SHIPPING_COUNTRY_IS_NOT_IN_SCHENGEN_AREA
                        "
                    >
                        Le pays de livraison ne fait pas partie de l'espace Schengen. Les paiements de la commande ne
                        seront pas acceptés automatiquement.
                    </div>

                    <div
                        v-if="anti_fraud_customer_order.reason.name === AntiFraudReason.HAS_INVALID_ARTICLES"
                        class="flex flex-col"
                    >
                        <span v-if="anti_fraud_customer_order.reason.details.length === 1"
                            >1 article de la commande nécessite une verification. Les paiements de la commande ne seront
                            pas acceptés automatiquement.</span
                        >
                        <span v-if="anti_fraud_customer_order.reason.details.length > 1"
                            >{{ anti_fraud_customer_order.reason.details.length }} articles de la commande nécessitent
                            une vérification. Les paiements de la commande ne seront pas acceptés automatiquement.</span
                        >
                        <span
                            v-for="(reason, idx) of anti_fraud_customer_order.reason.details"
                            :key="`${reason.sku}-${idx}`"
                            v-html="formatInvalidArticleDetails(reason)"
                        >
                        </span>
                    </div>
                </template>

                <!-- Anti Fraud rules applied on payments -->
                <div v-if="rejected_anti_fraud_customer_order_payments.length === 1">
                    {{ rejected_anti_fraud_customer_order_payments.length }} paiement ne passe pas les règles définies
                    par le module anti-fraude et nécessite une acceptation manuelle.
                </div>
                <div v-if="rejected_anti_fraud_customer_order_payments.length > 1">
                    {{ rejected_anti_fraud_customer_order_payments.length }} paiements ne passent pas les règles
                    définies par le module anti-fraude et nécessitent une acceptation manuelle.
                </div>
            </div>
        </div>
    </div>
</template>
